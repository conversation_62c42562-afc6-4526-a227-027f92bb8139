package assessment

import (
	"errors"
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/assessment"
	assessmentReq "github.com/flipped-aurora/gin-vue-admin/server/model/assessment/request"
	scoreModel "github.com/flipped-aurora/gin-vue-admin/server/model/score"
	"gorm.io/gorm"
)

type AssessmentCoefficientService struct{}

// ReplaceAssessmentCoefficients 替换考核系数（先删除后插入）- 使用新的两个表结构
func (s *AssessmentCoefficientService) ReplaceAssessmentCoefficients(req assessmentReq.ReplaceAssessmentCoefficientsRequest) error {
	// 开启事务
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 1. 删除该考核配置的所有原有记录
		// 先删除项目负责人评分记录（通过关联的系数分配ID）
		if err := tx.Where(`coefficient_allocation_id IN (
			SELECT id FROM assessment_coefficient_allocation
			WHERE assessment_config_id = ?
		)`, req.AssessmentConfigId).Delete(&assessment.ProjectManagerScore{}).Error; err != nil {
			global.GVA_LOG.Error(fmt.Sprintf("删除考核配置 %d 的项目负责人评分记录失败: %v", req.AssessmentConfigId, err))
			return fmt.Errorf("删除项目负责人评分数据失败: %v", err)
		}

		// 再删除系数分配记录
		if err := tx.Where("assessment_config_id = ?", req.AssessmentConfigId).
			Delete(&assessment.AssessmentCoefficientAllocation{}).Error; err != nil {
			global.GVA_LOG.Error(fmt.Sprintf("删除考核配置 %d 的系数分配记录失败: %v", req.AssessmentConfigId, err))
			return fmt.Errorf("删除系数分配数据失败: %v", err)
		}

		global.GVA_LOG.Info(fmt.Sprintf("已删除考核配置 %d 的原有记录", req.AssessmentConfigId))

		// 2. 如果有新记录，批量插入
		if len(req.Records) > 0 {
			// 转换为数据库模型
			var coefficientAllocations []assessment.AssessmentCoefficientAllocation

			for _, record := range req.Records {
				// 数据验证
				if record.AssessmentCoefficient < 0 || record.AssessmentCoefficient > 100 {
					return fmt.Errorf("用户 %s 项目 %d 的考核系数 %.2f 超出有效范围 [0-100]",
						record.Username, record.ProjectId, record.AssessmentCoefficient)
				}

				// 转换类型为指针类型
				assessmentConfigId := int(record.AssessmentConfigId)
				username := record.Username
				projectId := int(record.ProjectId)
				assessmentCoefficient := record.AssessmentCoefficient
				calculationParameter := record.CalculationParameter

				// 创建系数分配记录
				coefficientAllocation := assessment.AssessmentCoefficientAllocation{
					AssessmentConfigId:    &assessmentConfigId,
					Username:              &username,
					ProjectId:             &projectId,
					AssessmentCoefficient: &assessmentCoefficient,
					CalculationParameter:  &calculationParameter,
				}
				coefficientAllocations = append(coefficientAllocations, coefficientAllocation)
			}

			// 批量插入系数分配记录
			batchSize := 100
			for i := 0; i < len(coefficientAllocations); i += batchSize {
				end := i + batchSize
				if end > len(coefficientAllocations) {
					end = len(coefficientAllocations)
				}

				if err := tx.CreateInBatches(coefficientAllocations[i:end], batchSize).Error; err != nil {
					global.GVA_LOG.Error(fmt.Sprintf("批量插入系数分配记录失败: %v", err))
					return fmt.Errorf("插入系数分配数据失败: %v", err)
				}
			}

			global.GVA_LOG.Info(fmt.Sprintf("成功插入考核配置 %d 的 %d 条系数分配记录", req.AssessmentConfigId, len(coefficientAllocations)))
		}

		// 3. 验证数据完整性（可选）
		if err := s.validateCoefficientsSumNew(tx, req.AssessmentConfigId); err != nil {
			global.GVA_LOG.Warn(fmt.Sprintf("考核系数总和校验警告: %v", err))
			// 注意：这里只是警告，不回滚事务，因为前端已经做了校验
		}

		return nil
	})
}

// validateCoefficientsSum 验证考核系数总和（后端二次校验）
func (s *AssessmentCoefficientService) validateCoefficientsSum(tx *gorm.DB, assessmentConfigId uint) error {
	type UserCoefficientSum struct {
		Username string  `json:"username"`
		Total    float64 `json:"total"`
	}

	var results []UserCoefficientSum
	err := tx.Model(&scoreModel.ProjectAssessmentScore{}).
		Select("username, SUM(assessment_coefficient) as total").
		Where("assessment_config_id = ?", assessmentConfigId).
		Group("username").
		Find(&results).Error

	if err != nil {
		return fmt.Errorf("查询用户系数总和失败: %v", err)
	}

	var invalidUsers []string
	for _, result := range results {
		// 允许0.1的误差
		if result.Total < 99.9 || result.Total > 100.1 {
			invalidUsers = append(invalidUsers, fmt.Sprintf("%s(%.1f%%)", result.Username, result.Total))
		}
	}

	if len(invalidUsers) > 0 {
		return fmt.Errorf("以下用户的考核系数总和不为100%%: %v", invalidUsers)
	}

	return nil
}

// validateCoefficientsSumNew 验证考核系数总和（使用新表结构）
func (s *AssessmentCoefficientService) validateCoefficientsSumNew(tx *gorm.DB, assessmentConfigId uint) error {
	type UserCoefficientSum struct {
		Username string  `json:"username"`
		Total    float64 `json:"total"`
	}

	var results []UserCoefficientSum
	err := tx.Model(&assessment.AssessmentCoefficientAllocation{}).
		Select("username, SUM(assessment_coefficient) as total").
		Where("assessment_config_id = ?", assessmentConfigId).
		Group("username").
		Find(&results).Error

	if err != nil {
		return fmt.Errorf("查询用户系数总和失败: %v", err)
	}

	var invalidUsers []string
	for _, result := range results {
		// 允许0.1的误差
		if result.Total < 99.9 || result.Total > 100.1 {
			invalidUsers = append(invalidUsers, fmt.Sprintf("%s(%.1f%%)", result.Username, result.Total))
		}
	}

	if len(invalidUsers) > 0 {
		return fmt.Errorf("以下用户的考核系数总和不为100%%: %v", invalidUsers)
	}

	return nil
}

// GetAssessmentCoefficients 获取考核系数 - 使用新表结构但保持返回格式兼容
func (s *AssessmentCoefficientService) GetAssessmentCoefficients(assessmentConfigId uint) ([]scoreModel.ProjectAssessmentScore, error) {
	var assessmentScores []scoreModel.ProjectAssessmentScore

	// 从新的两个表联合查询，但转换为原有的返回格式以保持兼容性
	type CombinedResult struct {
		Id                    int      `json:"id"`
		AssessmentConfigId    int      `json:"assessmentConfigId"`
		Username              string   `json:"username"`
		ProjectId             int      `json:"projectId"`
		AssessmentCoefficient float64  `json:"assessmentCoefficient"`
		ManagerScore          *float64 `json:"managerScore"`
		ScorerUsername        *string  `json:"scorerUsername"`
		CalculationParameter  string   `json:"calculationParameter"`
	}

	var results []CombinedResult
	err := global.GVA_DB.Table("assessment_coefficient_allocation aca").
		Select(`aca.id, aca.assessment_config_id, aca.username, aca.project_id,
				aca.assessment_coefficient, pms.manager_score, pms.scorer_username,
				aca.calculation_parameter`).
		Joins("LEFT JOIN project_manager_score pms ON aca.id = pms.coefficient_allocation_id").
		Where("aca.assessment_config_id = ?", assessmentConfigId).
		Order("aca.username, aca.project_id").
		Scan(&results).Error

	if err != nil {
		global.GVA_LOG.Error(fmt.Sprintf("获取考核配置 %d 的系数失败: %v", assessmentConfigId, err))
		return nil, fmt.Errorf("获取考核系数失败: %v", err)
	}

	// 转换为原有的返回格式
	for _, result := range results {
		assessmentScore := scoreModel.ProjectAssessmentScore{
			Id:                    &result.Id,
			AssessmentConfigId:    &result.AssessmentConfigId,
			Username:              &result.Username,
			ProjectId:             &result.ProjectId,
			AssessmentCoefficient: &result.AssessmentCoefficient,
			ManagerScore:          result.ManagerScore,
			ScorerUsername:        result.ScorerUsername,
			CalculationParameter:  &result.CalculationParameter,
		}
		assessmentScores = append(assessmentScores, assessmentScore)
	}

	return assessmentScores, nil
}

// ValidateCoefficientsSum 校验考核系数总和 - 使用新表结构
func (s *AssessmentCoefficientService) ValidateCoefficientsSum(assessmentConfigId uint) (assessmentReq.ValidationResult, error) {
	var result assessmentReq.ValidationResult

	type UserCoefficientSum struct {
		Username string  `json:"username"`
		Total    float64 `json:"total"`
	}

	var userSums []UserCoefficientSum
	err := global.GVA_DB.Model(&assessment.AssessmentCoefficientAllocation{}).
		Select("username, SUM(assessment_coefficient) as total").
		Where("assessment_config_id = ?", assessmentConfigId).
		Group("username").
		Find(&userSums).Error

	if err != nil {
		return result, fmt.Errorf("查询用户系数总和失败: %v", err)
	}

	// 分析校验结果
	for _, userSum := range userSums {
		if userSum.Total < 99.9 || userSum.Total > 100.1 {
			// 不通过校验的用户
			invalidUser := assessmentReq.InvalidUserCoefficient{
				Username:   userSum.Username,
				Total:      userSum.Total,
				Difference: 100.0 - userSum.Total,
			}
			result.InvalidUsers = append(result.InvalidUsers, invalidUser)
		} else {
			// 通过校验的用户
			validUser := assessmentReq.ValidUserCoefficient{
				Username: userSum.Username,
				Total:    userSum.Total,
			}
			result.ValidUsers = append(result.ValidUsers, validUser)
		}
	}

	// 设置校验结果
	result.IsValid = len(result.InvalidUsers) == 0
	if result.IsValid {
		result.Message = fmt.Sprintf("所有 %d 个用户的考核系数总和均为100%%", len(result.ValidUsers))
	} else {
		result.Message = fmt.Sprintf("%d 个用户的考核系数总和不为100%%", len(result.InvalidUsers))
	}

	return result, nil
}

// ExportAssessmentCoefficients 导出考核系数分配Excel
func (s *AssessmentCoefficientService) ExportAssessmentCoefficients(assessmentConfigId uint) ([]byte, string, error) {
	// 获取考核系数数据
	_, err := s.GetAssessmentCoefficients(assessmentConfigId)
	if err != nil {
		return nil, "", err
	}

	// TODO: 实现Excel导出逻辑
	// 这里可以使用 github.com/xuri/excelize/v2 库来生成Excel文件
	// coefficients 变量包含了所有的考核系数数据，可以用来生成Excel

	// 临时返回空数据，实际应该生成Excel文件
	fileName := fmt.Sprintf("考核系数分配_%d.xlsx", assessmentConfigId)
	return []byte{}, fileName, errors.New("Excel导出功能待实现")
}
