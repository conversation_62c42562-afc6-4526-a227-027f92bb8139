
<template>
  <div class="project-assessment-score">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-loading-spinner />
      <p>正在加载考核数据...</p>
    </div>

    <!-- 主标签页 -->
    <el-tabs
      v-else
      v-model="editableTabsValue"
      type="card"
      class="demo-tabs"
    >
    <el-tab-pane
      v-for="item in editableTabs"
      :key="item.name"
      :label="item.title"
      :name="item.name"
    >
      <!-- 没有考核配置的情况 -->
      <div v-if="item.type === 'no_data'" class="no-data-container">
        <el-empty description="暂无考核任务">
          <template #description>
            <p>没有考核任务</p>
          </template>
        </el-empty>
      </div>

      <!-- 有考核配置的情况 -->
      <div v-else-if="item.type === 'assessment'">
        <!-- 自定义子标签页 -->
        <div class="custom-sub-tabs">
          <!-- 标签页头部 -->
          <div class="custom-tabs-header">
            <div
              v-for="tab in subTabsConfig"
              :key="tab.key"
              v-show="tab.visible"
              class="custom-tab-item"
              :class="{ 'active': (subTabsValue[item.name] || getFirstVisibleSubTab() || 'coefficient') === tab.key }"
              @click="handleSubTabClick(item.name, tab.key)"
            >
              {{ tab.label }}
            </div>
          </div>

          <!-- 标签页内容 -->
          <div class="custom-tabs-content">
            <!-- 无可见标签页时的提示 -->
            <div v-if="!hasVisibleSubTabs" class="empty-tab-content">
              <el-empty description="没有考核周期">
                <template #description>
                  <p>当前没有考核周期要考核</p>
                  
                </template>
              </el-empty>
            </div>
            <!-- 考核系数分配内容 -->
            <div v-show="(subTabsValue[item.name] || getFirstVisibleSubTab() || 'coefficient') === 'coefficient' && hasDepartmentMembersWithProjectParticipation" class="custom-tab-content">
              <!-- 操作按钮区域 -->
              <div class="mb-1 flex justify-center items-center">
                <div class="flex gap-2">
                  <el-button type="primary" @click="submitTabScores(item.name)">提交</el-button>
                  <el-button type="warning" @click="importDataTemplate(item.name)">导入数据模板</el-button>
                  <el-button type="success" @click="exportTabToExcel(item.name)">导出Excel</el-button>
                </div>
              </div>

            <!-- 考核系数分配表格 -->
            <div class="coefficient-table-container">
              <el-table
                :data="getCoefficientTableData(item.name)"
                stripe
                border
                style="width: 100%"
                :height="getTableHeight()"
                :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: 'bold' }"
                fit
                :show-summary="true"
                :summary-method="(param) => getCoefficientSummaryMethod(param, item.name)"
              >
                <el-table-column
                  prop="projectName"
                  label="项目名称"
                  min-width="150"
                  fixed="left"
                  align="center"
                />
                <el-table-column
                  v-for="member in getCurrentTabData(item.name).members"
                  :key="member.id"
                  :prop="`member_${member.id}`"
                  :label="member.name"
                  min-width="120"
                  align="center"
                >
                  <template #default="scope">
                    <div
                      v-if="isCoefficientCellEditable(scope.row.projectId, member.id)"
                      class="coefficient-cell editable-cell"
                      :contenteditable="true"
                      @blur="updateTabScore(item.name, scope.row.projectId, member.id, $event)"
                      @keydown="handleKeydown($event)"
                      @paste="handlePaste($event)"
                      @input="validateInput($event)"
                      @mouseenter="highlightCell"
                      @mouseleave="clearEmployeeHighlight"
                    >
                      {{ formatScoreDisplay(scope.row[`member_${member.id}`]) }}
                    </div>
                    <div
                      v-else
                      class="coefficient-cell non-editable-cell"
                      :title="`${member.name || member.userName} 不是项目 ${scope.row.projectName} 的参与者或负责人`"
                    >
                      -
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            </div>

            <!-- 项目成员评分内容 -->
            <div v-show="(subTabsValue[item.name] || getFirstVisibleSubTab() || 'coefficient') === 'projectMember' && hasManagerProjects" class="custom-tab-content">
              <!-- 操作按钮区域 -->
              <div class="mb-1">
                <!-- 操作按钮组 - Flex布局 -->
                <div class="flex items-center justify-center relative">
                  <!-- 高分限制信息 - 左侧 -->
                  <div class="high-score-limit-info mr-4">
                    <span class="limit-text">高分限制信息：</span>
                    <span class="limit-details">{{ getHighScoreLimitSummary(item.name) }}</span>
                  </div>

                  <!-- 按钮组 -->
                  <div class="flex gap-2">
                    <el-button type="primary" @click="submitProjectMemberScores(item.name)">提交</el-button>
                    <el-button type="success" @click="exportProjectMemberToExcel(item.name)">导出Excel</el-button>
                  </div>

                  <!-- 右侧占位，保持视觉平衡 -->
                  <div class="ml-4" style="width: 300px;"></div>
                </div>
              </div>

              <!-- 项目成员评分表格 -->
              <div class="project-member-table-container">
                <el-table
                  :data="getProjectMemberTableData(item.name)"
                  stripe
                  border
                  style="width: 100%"
                  :height="getTableHeight()"
                  :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: 'bold' }"
                  fit
                >
                  <el-table-column
                    prop="memberName"
                    label="成员姓名"
                    min-width="120"
                    fixed="left"
                    align="center"
                  />
                  <el-table-column
                    v-for="project in getCurrentManagerProjects(item.name)"
                    :key="project.projectId"
                    :prop="`project_${project.projectId}`"
                    :label="project.projectName"
                    min-width="120"
                    align="center"
                  >
                    <template #default="scope">
                      <div
                        v-if="isProjectMemberCombinationEditable(item.name, project.projectId, scope.row.userName)"
                        class="score-cell editable-cell"
                        :contenteditable="true"
                        @blur="updateProjectMemberScore(item.name, project.projectId, scope.row.userName, $event)"
                        @keydown="handleKeydown($event)"
                        @paste="handlePaste($event)"
                        @input="validateInput($event)"
                        @mouseenter="highlightCell"
                        @mouseleave="clearEmployeeHighlight"
                      >
                        {{ formatScoreDisplay(scope.row[`project_${project.projectId}`]) }}
                      </div>
                      <div v-else class="score-cell disabled-cell">
                        /
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>

            <!-- 员工评分内容 -->
            <div v-show="(subTabsValue[item.name] || getFirstVisibleSubTab() || 'coefficient') === 'orgMember' && isAdmin" class="custom-tab-content">
              <!-- 操作按钮区域 -->
              <div class="mb-1">
                <div class="employee-action-buttons">
                    <!-- 配额信息显示 -->
                    <div class="quota-info">
                      <span class="quota-text">当前高分配额(>=95分)剩余数量：</span>
                      <span class="quota-number">{{ getQuotaRemainingForTab(item.name) }}</span>
                    </div>
                    <el-button type="primary" @click="saveEmployeeScores(item.name)">提交评分</el-button>
                    <el-button @click="exportEmployeeScores(item.name)">导出Excel</el-button>
                    <!-- 奖金信息显示 -->
                    <div class="bonus-info">
                      <span class="bonus-text">剩余奖金额度：</span>
                      <span class="bonus-number">¥{{ getBonusRemainingForTab(item.name).toFixed(2) }}</span>
                    </div>
                  </div>
                <!-- <div class="text-center text-sm text-gray-500">
                  员工评分汇总表 - {{ item.title }}
                </div> -->
              </div>

              <!-- 员工评分表格 -->
              <div class="employee-score-table-container">
                <el-table
                  :data="getEmployeeScoreData(item.name)"
                  stripe
                  border
                  style="width: 100%"
                  :height="getTableHeight()"
                  :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: 'bold' }"
                  :row-class-name="getRowClassName"
                  fit
                >
                  <el-table-column
                    prop="memberName"
                    label="成员姓名"
                    min-width="120"
                    fixed="left"
                    align="center"
                  >
                    <template #default="scope">
                      <span>{{ scope.row.memberName }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="departmentManagerScore"
                    label="负责人评分"
                    min-width="120"
                    align="center"
                  >
                    <template #default="scope">
                      <div
                        v-if="scope.row.canEdit"
                        class="score-cell editable-cell"
                        :contenteditable="true"
                        @blur="updateEmployeeScore(item.name, scope.row, 'departmentManagerScore', $event)"
                        @keydown="handleKeydown($event)"
                        @paste="handlePaste($event)"
                        @input="validateInput($event)"
                        @mouseenter="highlightCell"
                        @mouseleave="clearEmployeeHighlight"
                      >
                        {{ formatEmployeeScoreDisplay(scope.row.departmentManagerScore) }}
                      </div>
                      <div v-else class="score-cell disabled-cell">
                        /
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="bonusAmount"
                    label="奖金"
                    min-width="120"
                    align="center"
                  >
                    <template #default="scope">
                      <div
                        class="bonus-cell editable-cell"
                        :contenteditable="true"
                        @blur="updateEmployeeBonus(item.name, scope.row, $event)"
                        @keydown="handleKeydown($event)"
                        @paste="handlePaste($event)"
                        @input="validateInput($event)"
                        @mouseenter="highlightCell"
                        @mouseleave="clearEmployeeHighlight"
                      >
                        {{ formatBonusDisplay(scope.row.bonusAmount) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="操作"
                    min-width="200"
                    align="center"
                    fixed="right"
                  >
                    <template #default="scope">
                      <div class="operation-buttons">
                        <el-button
                          type="success"
                          size="small"
                          @click="viewEmployeeDetail(scope.row)"
                        >
                          查看详情
                        </el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        {{ item.content }}
      </div>
    </el-tab-pane>
  </el-tabs>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, nextTick, onMounted, onUnmounted, watch, computed } from 'vue'
import * as XLSX from 'xlsx'
import { getUserInfo } from '@/api/user'
import {
  replaceAssessmentCoefficients,
  batchSubmitProjectMemberScores,
  batchSubmitDepartmentManagerScores
} from '@/api/score/projectAssessmentScore'
import { getSingleUserParameterScores } from '@/api/assessment/assessmentData'
import { calculateByRule } from '@/api/assessment/calculationMethods'
import { findProjectInfo } from '@/api/project/projectInfo'
import service from '@/utils/request'
import { useUserStore } from '@/pinia/modules/user'

// 导入公式计算器
import { FormulaCalculator } from '@/utils/socer/formulaCalculator.js'
import { ParameterMapper } from '@/utils/socer/parameterMapper.js'

defineOptions({
    name: 'ProjectAssessmentScore'
})

// 用户管理员状态
const isAdmin = ref(false)

// 用户代理负责人状态
const isAgentManager = ref(false)

// 用户是否有管理的项目（asManager有数据）
const hasManagerProjects = ref(false)

// 本部门是否有成员需要project_participation参数
const hasDepartmentMembersWithProjectParticipation = ref(false)

// 计算当前考核系数分配表格中的实际成员数量
const coefficientTableMembersCount = computed(() => {
  if (!assessmentData.value || !assessmentData.value.adminData || !assessmentData.value.adminData.orgMembers) {
    console.log('🔍 考核系数分配成员计数: 无数据，返回0')
    return 0
  }

  let count = 0
  assessmentData.value.adminData.orgMembers.forEach(member => {
    // 如果是代理负责人，直接排除
    if (member.isAgentManager) {
      console.log(`🔍 考核系数分配成员计数: 排除代理负责人 ${member.nickName}`)
      return
    }

    // 只有计算参数中包含project_participation的用户才计入
    if (hasProjectParticipationParameter(member)) {
      count++
      console.log(`🔍 考核系数分配成员计数: 包含成员 ${member.nickName}，当前计数: ${count}`)
    } else {
      console.log(`🔍 考核系数分配成员计数: 排除无project_participation参数的成员 ${member.nickName}`)
    }
  })

  console.log(`🔍 考核系数分配成员计数: 最终计数 ${count}`)
  return count
})

// 考核数据状态
const assessmentData = ref(null)
const loading = ref(false)

// 创建公式计算器实例
const formulaCalculator = new FormulaCalculator()
const parameterMapper = new ParameterMapper()

// 辅助函数：查找用户的计算方法
const findUserCalculationMethod = (userName, userData) => {
  console.log('🔍 查找用户计算方法:', userName)

  // 1. 首先检查API返回的计算方法
  if (userData.calculationMethod) {
    console.log('✅ 从API返回数据中找到计算方法')
    return userData.calculationMethod
  }

  // 2. 从assessmentData中查找
  if (assessmentData.value) {
    console.log('🔍 在assessmentData中查找计算方法...')

    // 检查管理员数据中的组织成员
    if (assessmentData.value.adminData && assessmentData.value.adminData.orgMembers) {
      const memberData = assessmentData.value.adminData.orgMembers.find(member =>
        member.userName === userName
      )
      if (memberData && memberData.calculationMethod) {
        console.log('✅ 从组织成员数据中找到计算方法')
        return memberData.calculationMethod
      }
    }

    // 检查当前用户数据
    if (assessmentData.value.currentUser && assessmentData.value.currentUser.userName === userName) {
      if (assessmentData.value.userCalculationMethod) {
        console.log('✅ 从当前用户数据中找到计算方法')
        return assessmentData.value.userCalculationMethod
      }
      if (assessmentData.value.currentUser.calculationMethod) {
        console.log('✅ 从当前用户对象中找到计算方法')
        return assessmentData.value.currentUser.calculationMethod
      }
    }

    // 检查用户数据列表
    if (assessmentData.value.userData && Array.isArray(assessmentData.value.userData)) {
      const userInList = assessmentData.value.userData.find(user => user.userName === userName)
      if (userInList && userInList.calculationMethod) {
        console.log('✅ 从用户数据列表中找到计算方法')
        return userInList.calculationMethod
      }
    }
  }

  console.log('❌ 未找到计算方法')
  return null
}

// 表格高度响应式变量
const tableHeight = ref(600)

// 员工奖金分配数据（响应式，按考核配置分组）
const employeeBonusData = reactive({})

// 剪贴板数据存储
const clipboardData = ref({
  value: null,
  type: null, // 'score' | 'bonus' | 'coefficient'
  sourceElement: null
})

// 获取字段类型
const getFieldType = (element) => {
  const classList = element.classList
  if (classList.contains('score-cell')) {
    return 'score'
  } else if (classList.contains('bonus-cell')) {
    return 'bonus'
  } else if (classList.contains('coefficient-cell')) {
    return 'coefficient'
  }
  return 'unknown'
}

// 显示复制反馈
const showCopyFeedback = (element) => {
  element.style.backgroundColor = '#e6f7ff'
  element.style.border = '2px solid #1890ff'

  setTimeout(() => {
    element.style.backgroundColor = ''
    element.style.border = ''
  }, 300)
}

// 验证粘贴数据
const validatePasteData = (data, targetElement) => {
  const fieldType = getFieldType(targetElement)
  const value = parseFloat(data.value)

  if (isNaN(value)) {
    ElMessage.warning('粘贴的数据必须是有效数字')
    return false
  }

  // 根据字段类型进行验证
  switch (fieldType) {
    case 'score':
      if (value < 0 || value > 100) {
        ElMessage.warning('评分必须在0-100之间')
        return false
      }
      break
    case 'bonus':
      if (value < 0) {
        ElMessage.warning('奖金金额不能为负数')
        return false
      }
      break
    case 'coefficient':
      if (value < 0 || value > 1) {
        ElMessage.warning('系数必须在0-1之间')
        return false
      }
      break
  }

  return true
}

// 格式化粘贴值
const formatPasteValue = (value, fieldType) => {
  const numValue = parseFloat(value)

  switch (fieldType) {
    case 'score':
    case 'bonus':
      return Math.round(numValue * 100) / 100
    case 'coefficient':
      return Math.round(numValue * 10000) / 10000
    default:
      return numValue
  }
}

// 员工评分数据（响应式，按考核配置分组）
const employeeScoreData = reactive({})

// 获取当前活跃考核配置的奖金剩余额度
const getBonusRemainingForTab = (tabName) => {
  if (!assessmentData.value || !assessmentData.value.adminData || !assessmentData.value.adminData.bonusInfo) {
    return 0
  }

  // 获取当前考核配置ID
  const currentTab = editableTabs.value.find(tab => tab.name === tabName)
  if (!currentTab || !currentTab.configId) {
    return 0
  }

  // 获取当前用户的组织ID
  const currentUserOrgId = getCurrentUserOrgId()

  // 根据考核配置ID获取对应的奖金信息
  const configKey = `${currentTab.configId}`
  const bonusInfo = assessmentData.value.adminData.bonusInfo[configKey]

  if (!bonusInfo || !bonusInfo.departmentAllocations) {
    return 0
  }

  // 查找当前部门的奖金信息
  const departmentBonus = bonusInfo.departmentAllocations.find(allocation =>
    allocation.departmentId === currentUserOrgId
  )

  if (!departmentBonus) {
    return 0
  }

  // 获取分配的总额度
  const allocatedAmount = departmentBonus.allocatedAmount || 0

  // 计算当前考核配置已分配的奖金总额
  let currentAllocatedAmount = 0
  const configBonusData = employeeBonusData[configKey] || {}

  Object.values(configBonusData).forEach(bonusAmount => {
    if (bonusAmount && bonusAmount > 0) {
      currentAllocatedAmount += parseFloat(bonusAmount)
    }
  })

  const remaining = Math.max(0, allocatedAmount - currentAllocatedAmount)
  return remaining
}

// 奖金剩余额度计算属性（基于当前活跃的tab）
const bonusRemainingAmount = computed(() => {
  // 获取当前活跃的员工评分tab
  const activeTabName = Object.keys(subTabsValue.value).find(tabName =>
    subTabsValue.value[tabName] === 'orgMember'
  )

  if (activeTabName) {
    return getBonusRemainingForTab(activeTabName)
  }

  // 如果没有明确的活跃tab，尝试获取第一个可用的tab
  const firstTab = editableTabs.value.find(tab => tab.name)
  if (firstTab) {
    return getBonusRemainingForTab(firstTab.name)
  }

  return 0
})

// 获取当前活跃考核配置的高分配额剩余数量
const getQuotaRemainingForTab = (tabName) => {
  if (!assessmentData.value || !assessmentData.value.adminData || !assessmentData.value.adminData.quotaInfo) {
    return 0
  }

  // 获取当前考核配置ID
  const currentTab = editableTabs.value.find(tab => tab.name === tabName)
  if (!currentTab || !currentTab.configId) {
    return 0
  }

  // 获取当前用户的组织ID
  const currentUserOrgId = getCurrentUserOrgId()

  // 查找当前部门的配额信息
  const quotaInfo = assessmentData.value.adminData.quotaInfo
  const departmentQuota = quotaInfo.departmentQuotas?.find(quota =>
    quota.departmentId === currentUserOrgId
  )

  if (!departmentQuota) {
    return 0
  }

  // 获取分配的总配额数量
  const allocatedQuota = departmentQuota.quotaAmount || 0

  // 计算当前考核配置已使用的高分配额数量
  let currentUsedQuota = 0
  const configKey = `${currentTab.configId}`
  const configScoreData = employeeScoreData[configKey] || {}

  Object.values(configScoreData).forEach(score => {
    if (score && parseFloat(score) >= 95) {
      currentUsedQuota += 1
    }
  })

  const remaining = Math.max(0, allocatedQuota - currentUsedQuota)
  return remaining
}

// 高分配额剩余数量计算属性（基于当前活跃的tab）
const quotaRemainingAmount = computed(() => {
  // 获取当前活跃的员工评分tab
  const activeTabName = Object.keys(subTabsValue.value).find(tabName =>
    subTabsValue.value[tabName] === 'orgMember'
  )

  if (activeTabName) {
    return getQuotaRemainingForTab(activeTabName)
  }

  // 如果没有明确的活跃tab，尝试获取第一个可用的tab
  const firstTab = editableTabs.value.find(tab => tab.name)
  if (firstTab) {
    return getQuotaRemainingForTab(firstTab.name)
  }

  return 0
})

// 获取考核数据的API函数
const getAssessmentData = async (orgId, isAdmin, userName) => {
  try {
    const res = await service({
      url: '/assessmentData/getAssessmentData',
      method: 'post',
      data: {
        orgId,
        isAdmin,
        userName
      }
    })
    return res
  } catch (error) {
    console.error('获取考核数据失败:', error)
    console.error('错误详情:', error.response?.data || error.message)
    throw error
  }
}

// 初始化考核数据和tabs
const initializeAssessmentData = async () => {
  loading.value = true
  try {
    // 统一从用户Store获取用户信息
    const userStore = useUserStore()

    // 确保用户信息已加载
    if (!userStore.userInfo || !userStore.userInfo.ID) {
      await userStore.GetUserInfo()
    }



    // 从用户Store中提取必要参数
    const orgId = userStore.userInfo?.OrgId || userStore.userInfo?.orgId || 5
    const userName = userStore.userInfo?.userName || userStore.userInfo?.username || userStore.userInfo?.UserName || '10112531'

    // 获取管理员状态和代理负责人状态（可以从API获取或从Store获取）
    const userRes = await getUserInfo()
    if (userRes.code === 0) {
      isAdmin.value = userRes.data.org?.is_admin || false
      isAgentManager.value = userRes.data.org?.is_agent_manager || false
    }



    // 如果无法从用户信息中获取用户名，使用默认值进行测试
    if (!userName) {
      userName = '10112531' // 临时使用默认用户名进行测试
    }


    const assessmentRes = await getAssessmentData(orgId, isAdmin.value, userName)

    if (assessmentRes.code === 0) {
      assessmentData.value = assessmentRes.data

      // 存储当前用户信息到assessmentData中，供后续使用
      assessmentData.value.currentUser = {
        userName: userName,
        userInfo: userRes.data.userInfo
      }

      // 检查当前登录用户是否有管理的项目
      checkUserManagerProjects(assessmentRes.data)

      // 检查本部门成员是否有project_participation参数
      checkDepartmentMembersProjectParticipation(assessmentRes.data)

      // 根据考核配置生成tabs
      generateTabsFromAssessmentConfigs(assessmentRes.data.assessmentConfigs)

      // 填充项目和人员数据到表格
      populateTabsData(assessmentRes.data)



      console.log('🔧 考核数据处理完成:', assessmentRes.data)
    } else {
      console.error('API返回错误:', assessmentRes)
      ElMessage.error(`获取考核数据失败: ${assessmentRes.msg || '未知错误'}`)
    }
  } catch (error) {
    console.error('初始化考核数据失败:', error)

    // 显示更详细的错误信息
    let errorMessage = '获取考核数据失败，请稍后重试'
    if (error.response?.data?.msg) {
      errorMessage = error.response.data.msg
    } else if (error.message) {
      errorMessage = error.message
    }

    ElMessage.error(errorMessage)
  } finally {
    loading.value = false
  }
}

// 检查当前登录用户是否有管理的项目
const checkUserManagerProjects = (data) => {
  let hasAsManagerProjects = false

  // 获取当前登录用户的用户名和组织ID
  const currentUserName = getCurrentUserName()
  const currentUserOrgId = getCurrentUserOrgId()

  // 在管理员数据中查找当前登录用户的项目信息
  if (data.adminData && data.adminData.orgMembers) {
    const currentUserMember = data.adminData.orgMembers.find(member =>
      member.userName === currentUserName
    )

    if (currentUserMember) {
      if (currentUserMember.projects && currentUserMember.projects.asManager) {
        // 过滤出属于当前用户组织节点的项目
        const orgProjects = currentUserMember.projects.asManager.filter(project => {
          const belongsToOrg = project.departmentId === currentUserOrgId
          return belongsToOrg
        })

        if (orgProjects.length > 0) {
          hasAsManagerProjects = true
        }
      }
    }
  }

  // 检查当前用户数据中的项目（如果直接提供了用户项目数据）
  if (data.userProjects && data.userProjects.asManager && data.userProjects.asManager.length > 0) {
    // 过滤出属于当前用户组织节点的项目
    const orgProjects = data.userProjects.asManager.filter(project => {
      const belongsToOrg = project.departmentId === currentUserOrgId
      return belongsToOrg
    })

    if (orgProjects.length > 0) {
      hasAsManagerProjects = true
    }
  }

  hasManagerProjects.value = hasAsManagerProjects
}

// 获取当前登录用户的用户名（统一从用户Store获取）
const getCurrentUserName = () => {
  const userStore = useUserStore()
  return userStore.userInfo?.userName || userStore.userInfo?.username || userStore.userInfo?.UserName || '10112531'
}

// 获取当前登录用户的组织ID（统一从用户Store获取）
const getCurrentUserOrgId = () => {
  const userStore = useUserStore()
  return userStore.userInfo?.OrgId || userStore.userInfo?.orgId || 5
}

// 检查本部门成员是否有project_participation参数
const checkDepartmentMembersProjectParticipation = (data) => {
  let hasProjectParticipationMembers = false

  // 检查管理员数据中的组织成员
  if (data.adminData && data.adminData.orgMembers) {
    // 遍历所有组织成员，检查是否有人需要project_participation参数
    data.adminData.orgMembers.forEach(member => {
      const hasParam = hasProjectParticipationParameter(member)
      if (hasParam) {
        hasProjectParticipationMembers = true
      }
    })
  }

  hasDepartmentMembersWithProjectParticipation.value = hasProjectParticipationMembers
}

// 根据考核配置生成tabs
const generateTabsFromAssessmentConfigs = (configs) => {
  if (!configs || configs.length === 0) {
    // 如果没有考核配置，显示一个默认tab
    editableTabs.value = [{
      title: '暂无考核任务',
      name: 'no_assessment',
      content: '',
      type: 'no_data'
    }]
    editableTabsValue.value = 'no_assessment'
    return
  }

  // 清空现有tabs
  editableTabs.value = []

  // 根据考核配置生成新的tabs
  configs.forEach((config) => {
    const tabName = `assessment_${config.id}`

    editableTabs.value.push({
      title: config.assessmentName, // 使用考核配置的名称作为tab标题
      name: tabName,
      content: '',
      type: 'assessment',
      configId: config.id,
      config: config
    })

    // 为每个tab初始化数据
    tabsData[tabName] = {
      members: [],
      projects: [],
      scores: {}
    }

    // 为每个tab初始化子标签页状态，选择第一个可见的标签页
    const firstVisibleTab = getFirstVisibleSubTab()
    subTabsValue[tabName] = firstVisibleTab || 'coefficient'
  })

  // 设置第一个tab为活动状态
  if (editableTabs.value.length > 0) {
    editableTabsValue.value = editableTabs.value[0].name
  }
}

// 填充tabs数据（项目和人员信息）
const populateTabsData = (data) => {
  // 提取项目信息
  const projects = extractProjectsData(data)

  // 提取人员信息
  const members = extractMembersData(data)

  // 为每个tab填充数据
  editableTabs.value.forEach(tab => {
    if (tab.type === 'assessment') {
      tabsData[tab.name] = {
        members: members,
        projects: projects,
        scores: {}
      }

      // 初始化评分数据为0
      projects.forEach(project => {
        members.forEach(member => {
          const key = `${project.id}-${member.id}`
          tabsData[tab.name].scores[key] = 0
        })
      })

      // 初始化项目成员评分数据
      initializeProjectMemberScores(tab.name, data)

      // 自动加载当前考核配置的系数数据
      loadCurrentAssessmentCoefficientData(tab, data)
    }
  })
}

// 自动加载当前考核配置的系数数据（页面初始化时调用）
const loadCurrentAssessmentCoefficientData = (tab, data) => {
  // 获取当前标签页对应的考核配置ID
  const configId = tab.configId

  // 查找对应考核配置的系数数据
  let coefficientsToLoad = []

  if (data.coefficientData) {
    // 优先使用 allCoefficients 字段，包含所有考核配置的数据
    if (data.coefficientData.allCoefficients && data.coefficientData.allCoefficients.length > 0) {
      // 从所有数据中筛选出当前标签页对应考核配置的数据
      coefficientsToLoad = data.coefficientData.allCoefficients.filter(coeff =>
        coeff.assessmentConfigId === configId
      )
    }
    // 如果没有 allCoefficients，使用原有逻辑作为兜底
    else {
      // 只加载当前考核配置数据，不加载历史数据
      if (data.coefficientData.currentAssessmentId === configId) {
        coefficientsToLoad = data.coefficientData.coefficients || []
      }
    }
  }

  // 将考核系数数据加载到表格中
  if (coefficientsToLoad.length > 0) {
    coefficientsToLoad.forEach(coeff => {
      // 构建表格中的key：项目ID-成员ID
      const userId = getUserIdByUsername(coeff.username, data)
      const key = `${coeff.projectId}-${userId}`

      if (tabsData[tab.name] && tabsData[tab.name].scores) {
        tabsData[tab.name].scores[key] = coeff.assessmentCoefficient
      }
    })
  }
}

// 加载已存在的考核系数数据到对应的标签页（导入数据模板时调用）
const loadExistingCoefficientData = (tab, data) => {
  // 获取当前标签页对应的考核配置ID
  const configId = tab.configId

  // 查找对应考核配置的系数数据
  let coefficientsToLoad = []

  if (data.coefficientData) {
    // 优先使用 allCoefficients 字段，包含所有考核配置的数据
    if (data.coefficientData.allCoefficients && data.coefficientData.allCoefficients.length > 0) {
      // 从所有数据中筛选出当前标签页对应考核配置的数据
      coefficientsToLoad = data.coefficientData.allCoefficients.filter(coeff =>
        coeff.assessmentConfigId === configId
      )
    }
    // 如果没有 allCoefficients，使用原有逻辑作为兜底
    else {
      // 检查当前考核配置数据
      if (data.coefficientData.currentAssessmentId === configId) {
        coefficientsToLoad = data.coefficientData.coefficients || []
      }
      // 检查历史考核配置数据
      else if (data.coefficientData.previousAssessmentId === configId) {
        coefficientsToLoad = data.coefficientData.previousCoefficients || []
      }
    }
  }

  // 将考核系数数据加载到表格中
  if (coefficientsToLoad.length > 0) {
    coefficientsToLoad.forEach(coeff => {
      // 构建表格中的key：项目ID-成员ID
      const userId = getUserIdByUsername(coeff.username, data)
      const key = `${coeff.projectId}-${userId}`

      if (tabsData[tab.name] && tabsData[tab.name].scores) {
        tabsData[tab.name].scores[key] = coeff.assessmentCoefficient
      }
    })
  }
}

// 根据用户名获取用户ID（用于构建表格key）
const getUserIdByUsername = (username, data) => {
  if (data.adminData && data.adminData.orgMembers) {
    const member = data.adminData.orgMembers.find(m => m.userName === username)
    return member ? member.userId : null
  }
  return null
}

// 格式化分数显示：0值显示为空，非0值正常显示，"-"标志直接显示
const formatScoreDisplay = (score) => {
  // 如果是"-"标志，直接返回
  if (score === '-') {
    return '-'
  }
  const numericScore = parseFloat(score) || 0
  return numericScore === 0 ? '' : numericScore
}

// 提取项目数据
const extractProjectsData = (data) => {
  const projects = []

  // 从管理员数据中提取项目（如果是管理员）
  if (data.adminData && data.adminData.orgMembers) {
    data.adminData.orgMembers.forEach(member => {
      if (member.projects) {
        // 添加作为负责人的项目
        if (member.projects.asManager) {
          member.projects.asManager.forEach(project => {
            if (!projects.find(p => p.id === project.projectId)) {
              projects.push({
                id: project.projectId,
                name: project.projectName,
                type: project.type || '项目',
                departmentName: project.departmentName,
                managerName: member.nickName
              })
            }
          })
        }

        // 添加作为成员的项目
        if (member.projects.asMember) {
          member.projects.asMember.forEach(project => {
            if (!projects.find(p => p.id === project.projectId)) {
              projects.push({
                id: project.projectId,
                name: project.projectName,
                type: project.type || '项目',
                departmentName: project.departmentName,
                managerName: project.managerName
              })
            }
          })
        }
      }
    })
  }

  // 从用户项目数据中提取项目
  if (data.managerProjects) {
    data.managerProjects.forEach(project => {
      if (!projects.find(p => p.id === project.projectId)) {
        projects.push({
          id: project.projectId,
          name: project.projectName,
          type: project.type || '项目',
          departmentName: project.departmentName,
          managerName: '当前用户'
        })
      }
    })
  }

  return projects
}

// 检查用户的计算参数是否包含project_participation
const hasProjectParticipationParameter = (member) => {
  // 检查用户是否有计算方法分配
  if (!member.calculationMethod || !member.calculationMethod.assignedParameters) {
    return false
  }

  // 检查分配的参数中是否包含project_participation
  const hasParam = member.calculationMethod.assignedParameters.some(param => {
    return param.parameterName === 'project_participation'
  })

  return hasParam
}

// 检查用户的计算参数是否包含project_manager_score
const hasProjectManagerScoreParameter = (member) => {
  // 检查用户是否有计算方法分配
  if (!member.calculationMethod || !member.calculationMethod.assignedParameters) {
    return false
  }

  // 检查分配的参数中是否包含project_manager_score
  const hasParam = member.calculationMethod.assignedParameters.some(param => {
    return param.parameterName === 'project_manager_score'
  })

  return hasParam
}

// 提取人员数据
const extractMembersData = (data) => {
  const members = []

  // 从管理员数据中提取组织成员（如果是管理员）
  if (data.adminData && data.adminData.orgMembers) {
    console.log('🔍 开始处理组织成员数据，总数:', data.adminData.orgMembers.length)

    data.adminData.orgMembers.forEach(member => {
      console.log(`🔍 处理成员: ${member.nickName} (${member.userName})`, {
        isAgentManager: member.isAgentManager,
        hasProjectParticipation: hasProjectParticipationParameter(member)
      })

      // 如果是代理负责人，直接排除，不需要其他参数验证
      if (member.isAgentManager) {
        console.log(`❌ 排除代理负责人: ${member.nickName}`)
        return
      }

      // 只有计算参数中包含project_participation的用户才显示在表格中
      if (hasProjectParticipationParameter(member)) {
        console.log(`✅ 添加成员到表格: ${member.nickName}`)
        members.push({
          id: member.userId,
          name: member.nickName,
          userName: member.userName,
          role: member.isAdmin ? '管理员' : '成员',
          email: member.email,
          phone: member.phone,
          calculationMethod: member.calculationMethod?.methodName || '未分配',
          assignedParameters: member.calculationMethod?.assignedParameters || [],
          isAgentManager: member.isAgentManager || false
        })
      } else {
        console.log(`❌ 排除无project_participation参数的成员: ${member.nickName}`)
      }
    })

    console.log('🔍 最终添加到表格的成员数量:', members.length)
  } else {
    // 如果不是管理员，检查当前用户是否有project_participation参数
    // 这里需要从data中获取当前用户的计算方法信息
    if (data.userCalculationMethod && hasProjectParticipationParameter({ calculationMethod: data.userCalculationMethod })) {
      members.push({
        id: 'current_user',
        name: '当前用户',
        userName: 'current',
        role: '成员',
        email: '',
        phone: '',
        calculationMethod: data.userCalculationMethod?.methodName || '未分配',
        assignedParameters: data.userCalculationMethod?.assignedParameters || []
      })
    }
  }

  return members
}

// 获取用户管理员状态（保留原有函数以兼容）
const fetchUserAdminStatus = async () => {
  try {
    const res = await getUserInfo()
    if (res.code === 0 && res.data.org) {
      isAdmin.value = res.data.org.is_admin || false
    }
  } catch (error) {
    console.warn('获取用户权限信息失败:', error)
    isAdmin.value = false
  }
}

// 子标签页配置
const subTabsConfig = computed(() => {
  const coefficientVisible = hasDepartmentMembersWithProjectParticipation.value && coefficientTableMembersCount.value > 0

  console.log('🔍 子标签页配置计算:', {
    hasDepartmentMembersWithProjectParticipation: hasDepartmentMembersWithProjectParticipation.value,
    coefficientTableMembersCount: coefficientTableMembersCount.value,
    coefficientVisible: coefficientVisible,
    hasManagerProjects: hasManagerProjects.value,
    isAdmin: isAdmin.value
  })

  const config = [
    {
      key: 'coefficient',
      label: '考核系数分配',
      visible: coefficientVisible // 当本部门有成员需要project_participation参数且实际成员数量大于0时显示
    },
    {
      key: 'projectMember',
      label: '项目成员评分',
      visible: hasManagerProjects.value // 当用户有管理的项目时显示
    },
    {
      key: 'orgMember',
      label: '员工评分',
      visible: isAdmin.value // 只有管理员可以看到
    }
  ]

  return config
})

// 检查是否有可见的子标签页
const hasVisibleSubTabs = computed(() => {
  return subTabsConfig.value.some(tab => tab.visible)
})

// 获取第一个可见的子标签页
const getFirstVisibleSubTab = () => {
  const visibleTab = subTabsConfig.value.find(tab => tab.visible)
  return visibleTab ? visibleTab.key : null
}

// Tabs 相关数据
const editableTabsValue = ref('')
const editableTabs = ref([])

// 为每个标签页存储独立的数据
const tabsData = reactive({})

// 为每个主标签页存储子标签页的活动状态
const subTabsValue = reactive({})

// 为每个标签页存储项目成员评分数据
const projectMemberScoresData = reactive({})

// 监听权限变化，确保用户不会停留在不可见的标签页
watch([isAdmin, hasManagerProjects, hasDepartmentMembersWithProjectParticipation], () => {
  Object.keys(subTabsValue).forEach(tabName => {
    const currentTab = subTabsValue[tabName]
    const isCurrentTabVisible = subTabsConfig.value.find(tab => tab.key === currentTab)?.visible

    if (!isCurrentTabVisible) {
      // 如果当前标签页不可见，切换到第一个可见的标签页
      const firstVisibleTab = getFirstVisibleSubTab()
      if (firstVisibleTab) {
        subTabsValue[tabName] = firstVisibleTab
      }
    }
  })
}, { immediate: true })





// 生成随机数据的函数
const generateRandomData = () => {
  // 随机生成成员数据
  const memberNames = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十', '郑十一', '王十二']
  const roles = ['前端开发', '后端开发', '产品经理', '测试工程师', 'UI设计师', '项目经理', '架构师', '运维工程师']

  const randomMembers = []
  const memberCount = Math.floor(Math.random() * 6) + 5 // 5-10个成员
  for (let i = 0; i < memberCount; i++) {
    randomMembers.push({
      id: i + 1,
      name: memberNames[Math.floor(Math.random() * memberNames.length)],
      role: roles[Math.floor(Math.random() * roles.length)]
    })
  }

  // 随机生成项目数据
  const projectNames = ['电商平台重构', '移动端APP开发', '数据分析系统', '智能监控平台', 'AI聊天机器人', '物联网管理系统', '区块链应用']
  const projectTypes = ['自研项目', '承揽项目']

  const randomProjects = []
  const projectCount = Math.floor(Math.random() * 4) + 3 // 3-6个项目
  for (let i = 0; i < projectCount; i++) {
    randomProjects.push({
      id: i + 1,
      name: projectNames[Math.floor(Math.random() * projectNames.length)] + `(${i + 1})`,
      type: projectTypes[Math.floor(Math.random() * projectTypes.length)]
    })
  }

  // 生成随机评分数据
  const randomScores = {}
  randomProjects.forEach(project => {
    randomMembers.forEach(member => {
      const score = Math.floor(Math.random() * 41) + 60 // 60-100分
      randomScores[`${project.id}-${member.id}`] = score
    })
  })

  return {
    members: randomMembers,
    projects: randomProjects,
    scores: randomScores
  }
}



// 假数据 - 人员列表
const members = ref([
  { id: 1, name: '张三', role: '前端开发' },
  { id: 2, name: '李四', role: '后端开发' },
  { id: 3, name: '王五', role: '产品经理' },
  { id: 4, name: '赵六', role: '测试工程师' },
  { id: 5, name: '钱七', role: 'UI设计师' },
  { id: 6, name: '孙八', role: '项目经理' },
  { id: 7, name: '周九', role: '架构师' },
  { id: 8, name: '吴十', role: '运维工程师' },
  { id: 9, name: '郑十一', role: '数据分析师' },
  { id: 10, name: '王十二', role: '算法工程师' },
  { id: 11, name: '李十三', role: '安全工程师' },
  { id: 12, name: '陈十四', role: '移动开发' },
  { id: 13, name: '林十五', role: '全栈开发' },
  { id: 14, name: '黄十六', role: '技术总监' },
  { id: 15, name: '刘十七', role: '质量保证' }
])

// 假数据 - 项目列表
const projects = ref([
  { id: 1, name: '电商平台重构项目', type: '承揽项目' },
  { id: 2, name: '移动端APP开发', type: '自研项目' },
  { id: 3, name: '数据分析系统', type: '承揽项目' },
  { id: 4, name: '客户管理系统', type: '自研项目' },
  { id: 5, name: '财务管理平台', type: '承揽项目' },
  { id: 6, name: '人力资源管理系统', type: '自研项目' },
  { id: 7, name: '供应链管理平台', type: '承揽项目' },
  { id: 8, name: '智能监控系统', type: '自研项目' },
  { id: 9, name: '在线教育平台', type: '承揽项目' },
  { id: 10, name: '物联网数据平台', type: '自研项目' },
  { id: 11, name: '区块链溯源系统', type: '承揽项目' },
  { id: 12, name: 'AI智能客服系统', type: '自研项目' }
])

// 评分数据存储 - 使用响应式对象存储所有评分
const scores = reactive({})

// 表格容器引用 - 支持多个标签页
const tableRefs = reactive({})
const tableWrapper = ref(null) // 保留兼容性

// 处理子标签页点击事件
const handleSubTabClick = (parentTabName, tabName) => {
  // 检查权限：如果不是管理员且尝试访问受限标签页，则阻止切换
  if (!isAdmin.value && tabName === 'orgMember') {
    ElMessage.warning('您没有权限访问此功能，请联系管理员')
    return
  }

  // 确保子标签页值存在
  if (!subTabsValue[parentTabName]) {
    const firstVisibleTab = getFirstVisibleSubTab()
    subTabsValue[parentTabName] = firstVisibleTab || 'coefficient'
  }

  subTabsValue[parentTabName] = tabName

  // 在子标签页切换后重新应用 sticky 定位
  nextTick(() => {
    ensureStickyPositioning()
  })
}

// 设置表格引用
const setTableRef = (el, tabName) => {
  if (el) {
    tableRefs[tabName] = el
  }
}



// 获取当前标签页数据
const getCurrentTabData = (tabName) => {
  if (tabName === 'projectAssessment') {
    return {
      members: members.value,
      projects: projects.value,
      scores: scores
    }
  }
  return tabsData[tabName] || { members: [], projects: [], scores: {} }
}

// 获取考核系数分配表格数据（转换为el-table格式）
const getCoefficientTableData = (tabName) => {
  const tabData = getCurrentTabData(tabName)
  const tableData = []

  tabData.projects.forEach(project => {
    const row = {
      projectId: project.id,
      projectName: project.name,
      projectType: project.type
    }

    // 为每个成员添加对应的系数值
    tabData.members.forEach(member => {
      const key = `${project.id}-${member.id}`
      row[`member_${member.id}`] = tabData.scores[key] || 0
    })

    tableData.push(row)
  })

  return tableData
}

// 获取考核系数分配表格的合计方法
const getCoefficientSummaryMethod = (param, tabName) => {
  const { columns, data } = param
  const sums = []
  const tabData = getCurrentTabData(tabName)

  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 查找对应的成员ID
    const memberIdMatch = column.property?.match(/member_(\d+)/)
    if (memberIdMatch) {
      const memberId = parseInt(memberIdMatch[1])
      const total = calculateTabMemberTotal(tabName, memberId)
      sums[index] = formatScoreDisplay(total)
    } else {
      sums[index] = ''
    }
  })

  return sums
}

// 更新指定标签页的评分
const updateTabScore = (tabName, projectId, memberId, event) => {
  const key = `${projectId}-${memberId}`
  const value = parseFloat(event.target.textContent) || 0

  // 确保标签页数据结构存在
  if (!tabsData[tabName]) {
    tabsData[tabName] = {
      members: [],
      projects: [],
      scores: {}
    }
  }
  if (!tabsData[tabName].scores) {
    tabsData[tabName].scores = {}
  }

  // 更新评分数据
  tabsData[tabName].scores[key] = value

  // 限制分数范围
  if (value > 100) {
    event.target.textContent = '100'
    tabsData[tabName].scores[key] = 100
  } else if (value < 0) {
    event.target.textContent = ''  // 0值显示为空
    tabsData[tabName].scores[key] = 0
  } else if (value === 0) {
    event.target.textContent = ''  // 0值显示为空
    tabsData[tabName].scores[key] = 0
  }
}

// 计算指定标签页成员的总分
const calculateTabMemberTotal = (tabName, memberId) => {
  let total = 0
  const tabData = getCurrentTabData(tabName)

  tabData.projects.forEach(project => {
    // 只有可编辑的单元格才参与合计计算
    if (isCoefficientCellEditable(project.id, memberId)) {
      const score = tabData.scores[`${project.id}-${memberId}`] || 0
      total += parseFloat(score) || 0
    }
  })
  return total.toFixed(1)
}

// 提交指定标签页的系数（先删除后插入方式）
const submitTabScores = async (tabName) => {
  const tabData = getCurrentTabData(tabName)

  // 校验每个人的考核系数是否为100%
  const validationResult = validateCoefficientsSum(tabName)

  if (!validationResult.isValid) {
    // 显示详细的校验错误信息
    ElMessageBox.alert(
      validationResult.detailMessage,
      '校验失败',
      {
        confirmButtonText: '确定',
        type: 'error',
        dangerouslyUseHTMLString: true
      }
    )
    return
  }

  // 校验通过，确认提交
  try {
    await ElMessageBox.confirm(
      '请勿随意提交，因为提交后会清空原有成员对应项目的评分记录，需要提醒对应项目负责人重新进行评分。\n\n确认要继续提交考核系数分配吗？',
      '重要提示',
      {
        confirmButtonText: '确定提交',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false,
        customClass: 'important-confirm-dialog'
      }
    )

    // 用户确认提交，开始提交流程
    await performSubmitWithReplacement(tabName, tabData)

  } catch (error) {
    if (error === 'cancel') {
      // 用户取消提交
      ElMessage({
        type: 'info',
        message: '已取消提交'
      })
    } else {
      // 提交过程中出错
      console.error('提交失败:', error)
      ElMessage({
        type: 'error',
        message: '提交失败，请重试'
      })
    }
  }
}

// 执行先删除后插入的提交流程
const performSubmitWithReplacement = async (tabName, tabData) => {
  const loadingMessage = ElMessage({
    type: 'info',
    message: '正在提交数据...',
    duration: 0 // 不自动关闭
  })

  try {
    // 准备提交数据
    const submitData = prepareSubmitData(tabName, tabData)

    // 调用后端API：先删除该考核配置中的原有记录，然后插入新数据
    const response = await submitCoefficientsWithReplacement(submitData)

    if (response.code === 0) {
      loadingMessage.close()
      ElMessage({
        type: 'success',
        message: '考核系数提交成功！'
      })

      // 刷新数据以获取最新信息
      await initializeAssessmentData()

    } else {
      throw new Error(response.msg || '提交失败')
    }

  } catch (error) {
    loadingMessage.close()
    console.error('提交过程出错:', error)

    ElMessageBox.alert(
      `提交失败：${error.message || '未知错误'}<br><br>请检查网络连接后重试，或联系系统管理员。`,
      '提交失败',
      {
        confirmButtonText: '确定',
        type: 'error',
        dangerouslyUseHTMLString: true
      }
    )

    throw error
  }
}

// 准备提交数据
const prepareSubmitData = (tabName, tabData) => {
  const assessmentConfigId = getCurrentAssessmentConfigId()
  const currentUserName = getCurrentUserName() // 获取当前登录用户名
  const submitRecords = []

  // 遍历所有成员和项目，构建提交数据（只提交可编辑的单元格）
  tabData.members.forEach(member => {
    tabData.projects.forEach(project => {
      // 只有可编辑的单元格才能提交
      if (isCoefficientCellEditable(project.id, member.id)) {
        const key = `${project.id}-${member.id}`
        const coefficient = tabData.scores[key] || 0

        // 只提交有值的记录
        if (coefficient > 0) {
          submitRecords.push({
            assessmentConfigId: assessmentConfigId,
            username: member.userName || member.name, // 被评价用户名
            projectId: project.id,
            assessmentCoefficient: coefficient,
            calculationParameter: 'project_participation', // 固定为 project_participation
            scorerUsername: currentUserName, // 评分人用户名（当前登录用户）
            createdAt: new Date().toISOString()
          })
        }
      }
    })
  })

  return {
    assessmentConfigId: assessmentConfigId,
    records: submitRecords,
    tabName: tabName,
    totalRecords: submitRecords.length
  }
}

// 调用后端API进行先删除后插入操作
const submitCoefficientsWithReplacement = async (submitData) => {
  try {
    const response = await replaceAssessmentCoefficients({
      assessmentConfigId: submitData.assessmentConfigId,
      records: submitData.records
    })

    return response

  } catch (error) {
    console.error('API调用失败:', error)
    throw new Error(error.response?.data?.msg || error.message || '网络请求失败')
  }
}

// 获取当前考核配置ID
const getCurrentAssessmentConfigId = () => {
  // 获取当前活动标签页对应的考核配置ID
  const currentTab = editableTabs.value.find(tab => tab.name === editableTabsValue.value)
  if (currentTab && currentTab.configId) {
    return currentTab.configId
  }

  // 如果找不到当前标签页，则返回第一个考核配置的ID作为默认值
  return assessmentData.value?.assessmentConfigs?.[0]?.id || 1
}

// 校验考核系数总和是否为100%
const validateCoefficientsSum = (tabName) => {
  const tabData = getCurrentTabData(tabName)
  const invalidMembers = []
  const validMembers = []

  // 检查每个成员的系数总和
  tabData.members.forEach(member => {
    let memberTotal = 0
    const memberDetails = []

    // 计算该成员在所有项目中的系数总和（只计算可编辑的单元格）
    tabData.projects.forEach(project => {
      // 只有可编辑的单元格才参与校验
      if (isCoefficientCellEditable(project.id, member.id)) {
        const key = `${project.id}-${member.id}`
        const score = tabData.scores[key] || 0
        memberTotal += parseFloat(score)

        if (score > 0) {
          memberDetails.push(`${project.name}: ${score}%`)
        }
      }
    })

    // 检查是否等于100%（允许0.1的误差）
    if (Math.abs(memberTotal - 100) > 0.1) {
      invalidMembers.push({
        name: member.name,
        total: memberTotal.toFixed(1),
        details: memberDetails,
        difference: (100 - memberTotal).toFixed(1)
      })
    } else {
      validMembers.push({
        name: member.name,
        total: memberTotal.toFixed(1)
      })
    }
  })

  if (invalidMembers.length > 0) {
    // 构建详细的错误信息
    let detailMessage = '<div style="text-align: left;">'
    detailMessage += '<p><strong>校验失败：以下人员的考核系数总和不为100%</strong></p>'
    detailMessage += '<div style="margin: 10px 0; padding: 10px; background-color: #fef2f2; border-left: 4px solid #ef4444; border-radius: 4px;">'

    invalidMembers.forEach(member => {
      const status = parseFloat(member.total) > 100 ? '超出' : '不足'
      const statusColor = parseFloat(member.total) > 100 ? '#ef4444' : '#f97316'

      detailMessage += `<div style="margin-bottom: 8px;">`
      detailMessage += `<strong style="color: ${statusColor};">${member.name}</strong>: `
      detailMessage += `当前总和 <span style="color: ${statusColor}; font-weight: bold;">${member.total}%</span>`
      detailMessage += ` (${status} <span style="color: ${statusColor};">${Math.abs(parseFloat(member.difference))}%</span>)`

      if (member.details.length > 0) {
        detailMessage += `<br><span style="font-size: 12px; color: #666; margin-left: 20px;">${member.details.join(', ')}</span>`
      }
      detailMessage += `</div>`
    })

    detailMessage += '</div>'
    detailMessage += '<p style="color: #666; font-size: 14px;"><strong>要求：</strong>每个人的考核系数总和必须为100%</p>'

    // if (validMembers.length > 0) {
    //   detailMessage += '<p style="color: #059669; font-size: 14px;"><strong>已通过校验的人员：</strong>'
    //   detailMessage += validMembers.map(m => `${m.name}(${m.total}%)`).join('、') + '</p>'
    // }

    detailMessage += '</div>'

    const simpleMessage = `${invalidMembers.length}个人员的考核系数总和不为100%，请检查后重新提交`

    return {
      isValid: false,
      message: simpleMessage,
      detailMessage: detailMessage
    }
  }

  return {
    isValid: true,
    message: '校验通过',
    detailMessage: `所有 ${validMembers.length} 个人员的考核系数总和均为100%，可以提交。`
  }
}

// 导入数据模板（加载当前考核配置的系数数据）
const importDataTemplate = (tabName) => {
  if (!assessmentData.value || !assessmentData.value.coefficientData) {
    ElMessage({
      type: 'warning',
      message: '没有可导入的数据模板'
    })
    return
  }

  // 获取当前标签页对应的考核配置
  const currentTab = editableTabs.value.find(tab => tab.name === tabName)
  if (!currentTab || !currentTab.configId) {
    ElMessage({
      type: 'error',
      message: '无法获取当前标签页配置信息'
    })
    return
  }

  const configId = currentTab.configId
  const coefficientData = assessmentData.value.coefficientData

  // 检查是否有可导入的数据
  let hasData = false
  let dataSource = ''
  let availableConfigs = []

  // 检查 allCoefficients 中有哪些考核配置的数据
  if (coefficientData.allCoefficients && coefficientData.allCoefficients.length > 0) {
    const configIds = [...new Set(coefficientData.allCoefficients.map(coeff => coeff.assessmentConfigId))]
    availableConfigs = configIds.filter(id => id !== configId) // 排除当前配置

    if (availableConfigs.length > 0) {
      hasData = true
      dataSource = `其他考核配置 (${availableConfigs.join(', ')})`
    }
  }

  if (!hasData) {
    ElMessage({
      type: 'warning',
      message: `没有其他考核配置的数据可以导入到"${currentTab.title}"`
    })
    return
  }

  // 如果有多个可选配置，选择最新的一个
  const sourceConfigId = Math.max(...availableConfigs)

  // 手动构建导入数据
  const coefficientsToImport = coefficientData.allCoefficients.filter(coeff =>
    coeff.assessmentConfigId === sourceConfigId
  )

  // 先清空当前表格数据
  if (tabsData[currentTab.name] && tabsData[currentTab.name].scores) {
    Object.keys(tabsData[currentTab.name].scores).forEach(key => {
      tabsData[currentTab.name].scores[key] = 0
    })
  }

  // 将数据导入到当前标签页
  if (coefficientsToImport.length > 0) {
    coefficientsToImport.forEach(coeff => {
      const userId = getUserIdByUsername(coeff.username, assessmentData.value)
      const key = `${coeff.projectId}-${userId}`

      if (tabsData[currentTab.name] && tabsData[currentTab.name].scores) {
        tabsData[currentTab.name].scores[key] = coeff.assessmentCoefficient
      }
    })
  }

  ElMessage({
    type: 'success',
    message: `成功导入 ${coefficientsToImport.length} 条数据`
  })
}



// 导出指定标签页的Excel（导出当前表格数据）
const exportTabToExcel = async (tabName) => {
  try {
    const tabData = getCurrentTabData(tabName)

    // 检查是否有数据
    if (!tabData.projects || tabData.projects.length === 0) {
      ElMessage({
        type: 'warning',
        message: '当前表格没有项目数据，无法导出'
      })
      return
    }

    if (!tabData.members || tabData.members.length === 0) {
      ElMessage({
        type: 'warning',
        message: '当前表格没有成员数据，无法导出'
      })
      return
    }

    ElMessage({
      type: 'info',
      message: '正在生成Excel文件...',
      duration: 2000
    })

    // 准备Excel数据
    const excelData = prepareExcelData(tabName, tabData)

    // 创建工作簿
    const workbook = XLSX.utils.book_new()

    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet(excelData)

    // 设置列宽
    const colWidths = [
      { wch: 20 }, // 项目名称列
      { wch: 12 }, // 项目类型列
    ]

    // 为每个成员列设置宽度
    tabData.members.forEach(() => {
      colWidths.push({ wch: 15 })
    })

    // 添加合计列宽度
    colWidths.push({ wch: 15 })

    worksheet['!cols'] = colWidths

    // 添加工作表到工作簿
    const currentTab = editableTabs.value.find(tab => tab.name === tabName)
    const sheetName = currentTab ? currentTab.label : '考核系数分配'
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)

    // 生成Excel文件
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })

    // 创建Blob并下载
    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `考核系数分配_${tabName}_${new Date().toISOString().slice(0, 10)}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage({
      type: 'success',
      message: 'Excel文件导出成功'
    })

  } catch (error) {
    console.error('导出Excel失败:', error)
    ElMessage({
      type: 'error',
      message: '导出失败：' + (error.message || '未知错误')
    })
  }
}

// 准备Excel数据（二维数组格式）
const prepareExcelData = (tabName, tabData) => {
  const excelData = []

  // 构建表头
  const headers = ['项目名称', '项目类型']

  // 添加成员列标题
  tabData.members.forEach(member => {
    headers.push(member.name || member.userName || `用户${member.id}`)
  })

  // 添加合计列
  headers.push('合计')

  excelData.push(headers)

  // 构建数据行
  tabData.projects.forEach(project => {
    const row = [
      project.name || `项目${project.id}`,
      project.type || '项目'
    ]

    let projectTotal = 0

    // 添加每个成员的系数数据
    tabData.members.forEach(member => {
      const key = `${project.id}-${member.id}`
      let coefficient = tabData.scores[key] || 0

      // 检查单元格是否可编辑，如果不可编辑则显示为"-"
      if (!isCoefficientCellEditable(project.id, member.id)) {
        row.push('-')
      } else {
        // 转换为百分比格式
        const percentValue = (coefficient * 100).toFixed(1) + '%'
        row.push(coefficient > 0 ? percentValue : '')

        // 只有可编辑的单元格才参与项目合计
        projectTotal += parseFloat(coefficient) || 0
      }
    })

    // 添加项目合计（转换为百分比）
    row.push(projectTotal > 0 ? (projectTotal * 100).toFixed(1) + '%' : '')

    excelData.push(row)
  })

  // 构建合计行
  const totalRow = ['合计', '']

  // 计算每个成员的总系数
  tabData.members.forEach(member => {
    let memberTotal = 0

    tabData.projects.forEach(project => {
      // 只有可编辑的单元格才参与合计计算
      if (isCoefficientCellEditable(project.id, member.id)) {
        const key = `${project.id}-${member.id}`
        const coefficient = tabData.scores[key] || 0
        memberTotal += parseFloat(coefficient) || 0
      }
    })

    totalRow.push(memberTotal > 0 ? (memberTotal * 100).toFixed(1) + '%' : '')
  })

  // 添加总合计（空白，因为没有意义）
  totalRow.push('')

  excelData.push(totalRow)

  return excelData
}

// ==================== 项目成员评分相关函数 ====================

// 获取项目成员评分表格数据（转换为el-table格式）
const getProjectMemberTableData = (tabName) => {
  const members = getCurrentManagerProjectMembers(tabName)
  const projects = getCurrentManagerProjects(tabName)
  const tableData = []

  members.forEach(member => {
    const row = {
      userName: member.userName,
      memberName: member.nickName || member.userName
    }

    // 为每个项目添加对应的评分值
    projects.forEach(project => {
      const score = getProjectMemberScore(tabName, project.projectId, member.userName)
      row[`project_${project.projectId}`] = score === '-' ? 0 : score
    })

    tableData.push(row)
  })

  return tableData
}

// 获取所有项目（不过滤，作为基础数据）
const getAllManagerProjects = (tabName) => {
  if (!assessmentData.value || !assessmentData.value.managerProjects) {
    return []
  }

  // 获取当前用户的组织ID
  const currentUserOrgId = getCurrentUserOrgId()

  // 过滤出属于当前组织的项目
  const orgProjects = assessmentData.value.managerProjects.filter(project => {
    const belongsToOrg = project.departmentId === currentUserOrgId
    return belongsToOrg
  })

  return orgProjects
}

// 获取当前登录用户负责的项目列表（过滤出至少有一个可编辑成员的项目）
const getCurrentManagerProjects = (tabName) => {
  const allProjects = getAllManagerProjects(tabName)

  // 过滤出至少有一个可编辑成员的项目
  const filteredProjects = allProjects.filter(project => {
    // 获取所有成员（不过滤）
    const allMembers = getAllManagerProjectMembers(tabName)

    // 检查该项目是否至少有一个可编辑的成员
    const hasEditableMember = allMembers.some(member =>
      isProjectMemberCombinationEditable(tabName, project.projectId, member.userName)
    )

    return hasEditableMember
  })

  return filteredProjects
}

// 获取所有成员（不过滤，作为基础数据）
const getAllManagerProjectMembers = (tabName) => {
  const projects = getAllManagerProjects(tabName)
  const allMembers = []
  const memberMap = new Map()

  // 获取当前考核配置ID
  const currentAssessmentConfigId = getCurrentAssessmentConfigId()

  // 获取当前考核配置的系数分配数据
  const coefficientData = getCoefficientsForCurrentAssessment(currentAssessmentConfigId)

  // 构建项目-用户的组合集合，用于后续判断编辑权限
  const projectUserCombinations = new Set()
  coefficientData.forEach(coeff => {
    projectUserCombinations.add(`${coeff.projectId}-${coeff.username}`)
  })

  // 获取组织成员信息（包含计算方法）
  const orgMembers = assessmentData.value?.adminData?.orgMembers || []

  // 显示项目成员，但需要检查是否包含project_manager_score参数
  projects.forEach(project => {
    if (project.members && project.memberNames) {
      project.members.forEach((userName, index) => {
        if (!memberMap.has(userName)) {
          // 查找该用户的完整信息（包含计算方法）
          const memberInfo = orgMembers.find(member => member.userName === userName)

          if (memberInfo) {
            // 检查该成员是否包含project_manager_score参数
            const hasProjectManagerScore = hasProjectManagerScoreParameter(memberInfo)

            // 只有包含project_manager_score参数的成员才显示
            if (hasProjectManagerScore) {
              memberMap.set(userName, {
                userName: userName,
                nickName: project.memberNames[index] || userName,
                calculationMethod: memberInfo.calculationMethod
              })
              allMembers.push({
                userName: userName,
                nickName: project.memberNames[index] || userName,
                calculationMethod: memberInfo.calculationMethod
              })
            }
          }
        }
      })
    }
  })

  return allMembers
}

// 获取过滤后的成员列表（过滤出至少有一个可编辑项目的成员）
const getCurrentManagerProjectMembers = (tabName) => {
  const allMembers = getAllManagerProjectMembers(tabName)
  const allProjects = getAllManagerProjects(tabName)

  // 过滤出至少有一个可编辑项目的成员
  const filteredMembers = allMembers.filter(member => {
    // 检查该成员是否至少有一个可编辑的项目
    const hasEditableProject = allProjects.some(project =>
      isProjectMemberCombinationEditable(tabName, project.projectId, member.userName)
    )

    return hasEditableProject
  })

  return filteredMembers
}



// 获取项目成员评分
const getProjectMemberScore = (tabName, projectId, memberUserName) => {
  // 如果该组合不可编辑，返回"/"标志
  if (!isProjectMemberCombinationEditable(tabName, projectId, memberUserName)) {
    return '/'
  }

  if (!projectMemberScoresData[tabName]) {
    return 0
  }
  const key = `${projectId}-${memberUserName}`
  return projectMemberScoresData[tabName][key] || 0
}

// 更新项目成员评分
const updateProjectMemberScore = (tabName, projectId, memberUserName, event) => {
  // 检查该组合是否可编辑
  if (!isProjectMemberCombinationEditable(tabName, projectId, memberUserName)) {
    return
  }

  const key = `${projectId}-${memberUserName}`
  const value = parseFloat(event.target.textContent) || 0

  // 确保数据结构存在
  if (!projectMemberScoresData[tabName]) {
    projectMemberScoresData[tabName] = {}
  }

  // 更新评分数据
  projectMemberScoresData[tabName][key] = value

  // 限制分数范围
  if (value > 100) {
    event.target.textContent = '100'
    projectMemberScoresData[tabName][key] = 100
  } else if (value < 0) {
    event.target.textContent = ''  // 0值显示为空
    projectMemberScoresData[tabName][key] = 0
  } else if (value === 0) {
    event.target.textContent = ''  // 0值显示为空
    projectMemberScoresData[tabName][key] = 0
  }
}

// 计算项目成员总分（现在不使用，保留兼容性）
const calculateProjectMemberTotal = (tabName, memberUserName) => {
  let total = 0
  const projects = getCurrentManagerProjects(tabName)

  projects.forEach(project => {
    const score = getProjectMemberScore(tabName, project.projectId, memberUserName)
    // 只有可编辑的单元格才参与总分计算，跳过"-"标志
    if (score !== '-') {
      total += parseFloat(score) || 0
    }
  })
  return total.toFixed(1)
}

// 计算项目总分（横纵坐标颠倒后使用）
const calculateProjectTotal = (tabName, projectId) => {
  let total = 0
  const members = getCurrentManagerProjectMembers(tabName)

  members.forEach(member => {
    const score = getProjectMemberScore(tabName, projectId, member.userName)
    // 只有可编辑的单元格才参与总分计算，跳过"-"标志
    if (score !== '-') {
      total += parseFloat(score) || 0
    }
  })
  return total.toFixed(1)
}

// 提交项目成员评分
const submitProjectMemberScores = async (tabName) => {
  try {
    // 验证高分限制
    const projects = getCurrentManagerProjects(tabName)
    const violations = []

    projects.forEach(project => {
      const checkResult = checkHighScoreLimit(tabName, project.projectId)
      if (checkResult.isExceeded) {
        violations.push({
          projectName: project.projectName,
          current: checkResult.current,
          limit: checkResult.limit
        })
      }
    })

    if (violations.length > 0) {
      let message = '以下项目的高分人数超出限制：\n'
      violations.forEach(v => {
        message += `${v.projectName}: 当前${v.current}人≥95分，限制${v.limit}人\n`
      })
      message += '\n请调整评分后再提交。'

      ElMessageBox.alert(message, '评分验证失败', {
        confirmButtonText: '确定',
        type: 'warning'
      })
      return
    }

    // 收集评分数据
    const scoreData = collectProjectMemberScoreData(tabName)

    if (scoreData.length === 0) {
      ElMessage({
        type: 'warning',
        message: '没有可提交的评分数据'
      })
      return
    }

    // 确认提交
    const confirmResult = await ElMessageBox.confirm(
      `确定要提交 ${scoreData.length} 条项目成员评分数据吗？`,
      '确认提交',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    if (confirmResult !== 'confirm') {
      return
    }

    // 提交数据
    ElMessage({
      type: 'info',
      message: '正在提交评分数据...'
    })

    const response = await batchSubmitProjectMemberScores(scoreData)

    if (response.code === 0) {
      ElMessage({
        type: 'success',
        message: `项目成员评分提交成功！共提交 ${scoreData.length} 条数据`
      })

      // 可以在这里刷新数据或执行其他后续操作
      // await loadAssessmentData()
    } else {
      throw new Error(response.msg || '提交失败')
    }

  } catch (error) {
    console.error('提交项目成员评分失败:', error)
    ElMessage({
      type: 'error',
      message: '提交失败：' + (error.message || '未知错误')
    })
  }
}

// 收集项目成员评分数据
const collectProjectMemberScoreData = (tabName) => {
  const scoreData = []
  const projects = getCurrentManagerProjects(tabName)
  const members = getCurrentManagerProjectMembers(tabName)
  const currentAssessmentConfigId = getCurrentAssessmentConfigId()

  // 获取当前用户信息
  const currentUser = getCurrentUserName() || ''

  projects.forEach(project => {
    members.forEach(member => {
      // 只收集可编辑的评分数据
      if (isProjectMemberCombinationEditable(tabName, project.projectId, member.userName)) {
        const score = getProjectMemberScore(tabName, project.projectId, member.userName)

        // 只有评分大于0的才提交
        if (score !== '-' && parseFloat(score) > 0) {
          // 查找对应的考核系数分配ID
          const coefficientData = getCoefficientsForCurrentAssessment(currentAssessmentConfigId)
          const coefficientRecord = coefficientData.find(coeff =>
            coeff.projectId === project.projectId && coeff.username === member.userName
          )

          if (coefficientRecord) {
            scoreData.push({
              coefficientAllocationId: coefficientRecord.id,
              managerScore: parseFloat(score),
              scorerUsername: currentUser,
              calculationParameter: 'project_manager_score'
            })
          }
        }
      }
    })
  })

  return scoreData
}



// 导出项目成员评分Excel
const exportProjectMemberToExcel = async (tabName) => {
  ElMessage({
    type: 'info',
    message: '项目成员评分Excel导出功能开发中...'
  })
  // TODO: 实现项目成员评分Excel导出逻辑
}

// 获取当前考核配置的系数分配数据
const getCoefficientsForCurrentAssessment = (assessmentConfigId) => {
  if (!assessmentData.value || !assessmentData.value.coefficientData) {
    return []
  }

  const coefficientData = assessmentData.value.coefficientData

  // 优先使用 allCoefficients 字段
  if (coefficientData.allCoefficients && coefficientData.allCoefficients.length > 0) {
    return coefficientData.allCoefficients.filter(coeff =>
      coeff.assessmentConfigId === assessmentConfigId
    )
  }

  // 兜底逻辑：使用当前或历史数据
  if (coefficientData.hasCurrentData &&
      coefficientData.currentAssessmentId === assessmentConfigId) {
    return coefficientData.coefficients || []
  }

  if (!coefficientData.hasCurrentData &&
      coefficientData.previousAssessmentId === assessmentConfigId) {
    return coefficientData.previousCoefficients || []
  }

  return []
}

// 判断项目-用户组合是否可编辑
const isProjectMemberCombinationEditable = (tabName, projectId, memberUserName) => {
  // 获取当前考核配置ID
  const currentAssessmentConfigId = getCurrentAssessmentConfigId()

  // 获取当前考核配置的系数分配数据
  const coefficientData = getCoefficientsForCurrentAssessment(currentAssessmentConfigId)

  // 检查该项目-用户组合是否在考核系数分配中存在
  const combination = `${projectId}-${memberUserName}`
  const hasCoefficient = coefficientData.some(coeff =>
    coeff.projectId === projectId && coeff.username === memberUserName
  )

  return hasCoefficient
}

// 判断考核系数分配单元格是否可编辑（根据用户是否为项目参与者或负责人）
const isCoefficientCellEditable = (projectId, userId) => {
  if (!assessmentData.value) {
    return false
  }

  // 根据userId获取用户名
  const userName = getUserNameById(userId)
  if (!userName) {
    return false
  }

  // 检查用户是否为该项目的负责人
  const isProjectManager = isUserProjectManager(projectId, userName)

  // 检查用户是否为该项目的参与者（成员）
  const isProjectMember = isUserProjectMember(projectId, userName)

  return isProjectManager || isProjectMember
}

// 根据用户ID获取用户名
const getUserNameById = (userId) => {
  if (!assessmentData.value || !assessmentData.value.adminData || !assessmentData.value.adminData.orgMembers) {
    return null
  }

  const member = assessmentData.value.adminData.orgMembers.find(m => m.userId === userId)
  return member ? member.userName : null
}

// 检查用户是否为项目负责人
const isUserProjectManager = (projectId, userName) => {
  // 检查managerProjects中的数据
  if (assessmentData.value.managerProjects) {
    const isManagerInManagerProjects = assessmentData.value.managerProjects.some(project =>
      project.projectId === projectId
    )
    if (isManagerInManagerProjects) {
      return true
    }
  }

  // 检查adminData.orgMembers中的项目负责人信息
  if (assessmentData.value.adminData && assessmentData.value.adminData.orgMembers) {
    const userMember = assessmentData.value.adminData.orgMembers.find(member =>
      member.userName === userName
    )

    if (userMember && userMember.projects && userMember.projects.asManager) {
      return userMember.projects.asManager.some(project => project.projectId === projectId)
    }
  }

  return false
}

// 检查用户是否为项目成员
const isUserProjectMember = (projectId, userName) => {
  // 检查managerProjects中的成员信息
  if (assessmentData.value.managerProjects) {
    const project = assessmentData.value.managerProjects.find(p => p.projectId === projectId)
    if (project && project.members && project.members.includes(userName)) {
      return true
    }
  }

  // 检查adminData.orgMembers中的项目成员信息
  if (assessmentData.value.adminData && assessmentData.value.adminData.orgMembers) {
    const userMember = assessmentData.value.adminData.orgMembers.find(member =>
      member.userName === userName
    )

    if (userMember && userMember.projects && userMember.projects.asMember) {
      return userMember.projects.asMember.some(project => project.projectId === projectId)
    }
  }

  return false
}

// 获取项目中可编辑的成员数量
const getEditableMemberCount = (tabName, projectId) => {
  const members = getCurrentManagerProjectMembers(tabName)
  let editableCount = 0

  members.forEach(member => {
    if (isProjectMemberCombinationEditable(tabName, projectId, member.userName)) {
      editableCount++
    }
  })

  return editableCount
}

// 获取项目的高分限制人数（95分以上的人数限制）
const getHighScoreLimit = (tabName, projectId) => {
  const editableCount = getEditableMemberCount(tabName, projectId)
  // 30%四舍五入
  const limit = Math.round(editableCount * 0.3)
  return limit
}

// 检查项目的高分人数是否超限
const checkHighScoreLimit = (tabName, projectId) => {
  const members = getCurrentManagerProjectMembers(tabName)
  const limit = getHighScoreLimit(tabName, projectId)
  let highScoreCount = 0

  members.forEach(member => {
    const score = getProjectMemberScore(tabName, projectId, member.userName)
    if (score !== '-' && parseFloat(score) >= 95) {
      highScoreCount++
    }
  })

  return {
    current: highScoreCount,
    limit: limit,
    isExceeded: highScoreCount > limit
  }
}

// 获取高分限制汇总信息
const getHighScoreLimitSummary = (tabName) => {
  const projects = getCurrentManagerProjects(tabName)

  if (projects.length === 0) {
    return '暂无项目数据'
  }

  const summaryParts = projects.map(project => {
    const editableCount = getEditableMemberCount(tabName, project.projectId)
    const limit = getHighScoreLimit(tabName, project.projectId)
    const checkResult = checkHighScoreLimit(tabName, project.projectId)

    return `${project.projectName}(${checkResult.current}/${limit})`
  })

  return summaryParts.join('，')
}

// 初始化项目成员评分数据
const initializeProjectMemberScores = (tabName, data) => {
  if (!data.managerProjects) {
    return
  }

  // 初始化该标签页的项目成员评分数据
  projectMemberScoresData[tabName] = {}

  // 获取当前考核配置ID
  const currentAssessmentConfigId = getCurrentAssessmentConfigId()

  // 获取当前考核配置的系数分配数据
  const coefficientData = getCoefficientsForCurrentAssessment(currentAssessmentConfigId)

  // 构建项目-用户的组合集合
  const projectUserCombinations = new Set()
  coefficientData.forEach(coeff => {
    projectUserCombinations.add(`${coeff.projectId}-${coeff.username}`)
  })

  // 为所有项目-用户组合初始化评分数据（包括不可编辑的）
  console.log('🔍 初始化项目成员评分数据 - managerProjects:', data.managerProjects)

  data.managerProjects.forEach(project => {
    console.log('🔍 处理项目:', project)
    if (project.members) {
      project.members.forEach(memberUserName => {
        const key = `${project.projectId}-${memberUserName}`
        projectMemberScoresData[tabName][key] = 0
        console.log('🔍 初始化key:', key)
      })
    }
  })

  console.log('🔍 初始化完成后的projectMemberScoresData:', projectMemberScoresData[tabName])

  // 加载已有的项目成员评分数据
  loadExistingProjectMemberScores(tabName, data, currentAssessmentConfigId)
}

// 加载已有的项目成员评分数据
const loadExistingProjectMemberScores = (tabName, data, assessmentConfigId) => {
  console.log('🔍 加载项目成员评分数据 - 开始')
  console.log('🔍 tabName:', tabName)
  console.log('🔍 assessmentConfigId:', assessmentConfigId)
  console.log('🔍 data.coefficientData:', data.coefficientData)

  if (!data.coefficientData || !data.coefficientData.allCoefficients) {
    console.log('🔍 没有系数数据，退出')
    return
  }

  console.log('🔍 所有系数数据:', data.coefficientData.allCoefficients)

  // 筛选出当前考核配置的数据
  const currentConfigCoefficients = data.coefficientData.allCoefficients.filter(coeff =>
    coeff.assessmentConfigId === assessmentConfigId
  )

  console.log('🔍 当前考核配置的系数数据:', currentConfigCoefficients)

  // 加载已有的评分数据
  currentConfigCoefficients.forEach(coeff => {
    console.log('🔍 处理系数记录:', coeff)

    // 只加载有评分数据的记录
    if (coeff.managerScore !== null && coeff.managerScore !== undefined) {
      const key = `${coeff.projectId}-${coeff.username}`
      console.log('🔍 生成key:', key, '评分:', coeff.managerScore)

      if (projectMemberScoresData[tabName] && projectMemberScoresData[tabName].hasOwnProperty(key)) {
        projectMemberScoresData[tabName][key] = coeff.managerScore
        console.log('✅ 成功加载评分:', key, '=', coeff.managerScore)
      } else {
        console.log('❌ key不存在于projectMemberScoresData中:', key)
        console.log('🔍 projectMemberScoresData[tabName]:', projectMemberScoresData[tabName])
      }
    } else {
      console.log('🔍 跳过无评分数据的记录:', coeff)
    }
  })

  console.log('🔍 加载完成后的projectMemberScoresData:', projectMemberScoresData[tabName])
}

// 确保sticky定位正确应用
const ensureStickyPositioning = () => {
  const applySticky = (tableElement) => {
    if (tableElement) {
      // 强制重新计算布局 - 表头固定
      const thead = tableElement.querySelector('thead')
      if (thead) {
        thead.style.setProperty('position', 'sticky', 'important')
        thead.style.setProperty('top', '0px', 'important')
        thead.style.setProperty('z-index', '250', 'important')
      }

      // 强制重新计算布局 - 左侧固定列
      const projectCells = tableElement.querySelectorAll('.project-name, .project-header')
      projectCells.forEach(cell => {
        cell.style.setProperty('position', 'sticky', 'important')
        cell.style.setProperty('left', '0px', 'important')
        if (cell.classList.contains('project-header')) {
          cell.style.setProperty('top', '0px', 'important')
          cell.style.setProperty('z-index', '300', 'important')
        } else {
          cell.style.setProperty('z-index', '50', 'important')
        }
      })

      // 强制重新计算布局 - 底部固定合计行
      const totalCells = tableElement.querySelectorAll('.total-row td')
      totalCells.forEach(cell => {
        cell.style.setProperty('position', 'sticky', 'important')
        cell.style.setProperty('bottom', '0px', 'important')
        cell.style.setProperty('display', 'table-cell', 'important')
        cell.style.setProperty('transform', 'translateZ(0)', 'important')
        cell.style.setProperty('will-change', 'transform', 'important')
        if (cell.classList.contains('total-label')) {
          cell.style.setProperty('left', '0px', 'important')
          cell.style.setProperty('z-index', '1000', 'important')
        } else {
          cell.style.setProperty('z-index', '999', 'important')
        }
      })

      // 确保 tfoot 也有 sticky 定位
      const tfoot = tableElement.querySelector('tfoot')
      if (tfoot) {
        tfoot.style.setProperty('position', 'sticky', 'important')
        tfoot.style.setProperty('bottom', '0px', 'important')
        tfoot.style.setProperty('z-index', '999', 'important')
        tfoot.style.setProperty('display', 'table-footer-group', 'important')
        tfoot.style.setProperty('transform', 'translateZ(0)', 'important')
        tfoot.style.setProperty('will-change', 'transform', 'important')
      }

      // 确保合计行始终可见
      const totalRow = tableElement.querySelector('.total-row')
      if (totalRow) {
        totalRow.style.setProperty('position', 'sticky', 'important')
        totalRow.style.setProperty('bottom', '0px', 'important')
        totalRow.style.setProperty('z-index', '999', 'important')
        totalRow.style.setProperty('display', 'table-row', 'important')
        totalRow.style.setProperty('transform', 'translateZ(0)', 'important')
        totalRow.style.setProperty('will-change', 'transform', 'important')
      }
    }
  }

  // 应用到当前活动的标签页
  nextTick(() => {
    const currentTabRef = tableRefs[editableTabsValue.value] || tableWrapper.value
    if (currentTabRef) {
      setTimeout(() => applySticky(currentTabRef), 100)
      setTimeout(() => applySticky(currentTabRef), 300)
      setTimeout(() => applySticky(currentTabRef), 500)
    }

    // 也应用到所有其他标签页
    Object.values(tableRefs).forEach(ref => {
      if (ref) {
        setTimeout(() => applySticky(ref), 100)
      }
    })
  })
}

// 初始化评分数据
const initializeScores = () => {
  projects.value.forEach(project => {
    members.value.forEach(member => {
      const key = `${project.id}-${member.id}`
      if (!scores[key]) {
        // 生成一些随机的初始评分数据用于演示
        scores[key] = Math.floor(Math.random() * 40) + 60 // 60-100之间的随机分数
      }
    })
  })
}

// 更新评分
const updateScore = (projectId, memberId, event) => {
  const key = `${projectId}-${memberId}`
  const value = parseFloat(event.target.textContent) || 0

  // 验证范围
  if (value < 0) {
    scores[key] = 0
    event.target.textContent = 0
  } else if (value > 100) {
    scores[key] = 100
    event.target.textContent = 100
  } else {
    scores[key] = Math.round(value * 10) / 10 // 保留一位小数
    event.target.textContent = scores[key]
  }
}

// 处理键盘事件
const handleKeydown = (event) => {
  // 处理复制快捷键 Ctrl+C
  if (event.ctrlKey && event.key.toLowerCase() === 'c') {
    event.preventDefault()
    const value = event.target.textContent.trim()
    const fieldType = getFieldType(event.target)

    clipboardData.value = {
      value: value,
      type: fieldType,
      sourceElement: event.target
    }

    showCopyFeedback(event.target)
    return
  }

  // 处理粘贴快捷键 Ctrl+V
  if (event.ctrlKey && event.key.toLowerCase() === 'v') {
    event.preventDefault()

    if (clipboardData.value && clipboardData.value.value !== null) {
      const pasteData = {
        value: clipboardData.value.value,
        type: clipboardData.value.type
      }

      if (validatePasteData(pasteData, event.target)) {
        const fieldType = getFieldType(event.target)
        const formattedValue = formatPasteValue(pasteData.value, fieldType)

        event.target.textContent = formattedValue
        // 触发blur事件以保存数据
        setTimeout(() => {
          event.target.blur()
        }, 10)

      }
    } else {
      ElMessage.warning('剪贴板中没有可粘贴的数值')
    }
    return
  }

  // 只允许数字、小数点、退格、删除、方向键
  const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Tab', 'Enter']
  const isNumber = /^[0-9]$/.test(event.key)
  const isDot = event.key === '.'

  if (!isNumber && !isDot && !allowedKeys.includes(event.key)) {
    event.preventDefault()
  }

  // 回车键确认编辑
  if (event.key === 'Enter') {
    event.target.blur()
    event.preventDefault()
  }

  // 限制只能有一个小数点
  if (isDot && event.target.textContent.includes('.')) {
    event.preventDefault()
  }
}

// 处理粘贴事件（支持浏览器原生粘贴）
const handlePaste = (event) => {
  event.preventDefault()

  const pastedText = event.clipboardData.getData('text').trim()
  const numValue = parseFloat(pastedText)

  if (isNaN(numValue)) {
    ElMessage.warning('粘贴的内容必须是有效数字')
    return
  }

  const fieldType = getFieldType(event.target)
  const pasteData = { value: numValue, type: fieldType }

  if (validatePasteData(pasteData, event.target)) {
    const formattedValue = formatPasteValue(numValue, fieldType)
    event.target.textContent = formattedValue

    // 触发blur事件以保存数据
    setTimeout(() => {
      event.target.blur()
    }, 10)

  }
}

// 验证输入内容
const validateInput = (event) => {
  const value = event.target.textContent
  // 移除非数字和小数点的字符
  const cleanValue = value.replace(/[^0-9.]/g, '')

  if (cleanValue !== value) {
    event.target.textContent = cleanValue
    // 将光标移到末尾
    const range = document.createRange()
    const selection = window.getSelection()
    range.selectNodeContents(event.target)
    range.collapse(false)
    selection.removeAllRanges()
    selection.addRange(range)
  }
}

// 高亮十字效果（只高亮左侧和上侧）
const highlightCrossHair = (rowId, colIndex) => {
  // 清除之前的高亮
  clearHighlight()

  // 高亮当前行左侧的单元格（包括项目名称列）
  const rowCells = document.querySelectorAll(`[data-row="${rowId}"]`)
  rowCells.forEach(cell => {
    const cellColIndex = cell.getAttribute('data-col')
    // 高亮项目名称列（没有data-col属性）或左侧的单元格
    if (!cellColIndex || parseInt(cellColIndex) < colIndex) {
      cell.classList.add('row-highlight')
    }
  })

  // 高亮当前列上侧的单元格（包括表头）
  const colCells = document.querySelectorAll(`[data-col="${colIndex}"]`)

  // 获取所有行的顺序（通过DOM结构）
  const allRows = []
  const tbody = document.querySelector('tbody')
  if (tbody) {
    const rows = tbody.querySelectorAll('tr')
    rows.forEach((row, index) => {
      const firstCell = row.querySelector('[data-row]')
      if (firstCell) {
        allRows.push({
          rowId: firstCell.getAttribute('data-row'),
          index: index
        })
      }
    })
  }

  const currentRowInfo = allRows.find(r => r.rowId === String(rowId))

  colCells.forEach(cell => {
    const cellRowId = cell.getAttribute('data-row')
    // 高亮表头（没有data-row属性）
    if (!cellRowId) {
      cell.classList.add('col-highlight')
    } else {
      // 对于数据行，检查是否在当前行上方
      const cellRowInfo = allRows.find(r => r.rowId === cellRowId)
      if (currentRowInfo && cellRowInfo && cellRowInfo.index < currentRowInfo.index) {
        cell.classList.add('col-highlight')
      }
    }
  })

  // 高亮当前单元格（交叉点）
  const currentCell = document.querySelector(`[data-row="${rowId}"][data-col="${colIndex}"]`)
  if (currentCell) {
    currentCell.classList.add('current-highlight')
  }
}

// 清除高亮效果
const clearHighlight = () => {
  const highlightedCells = document.querySelectorAll('.row-highlight, .col-highlight, .current-highlight')
  highlightedCells.forEach(cell => {
    cell.classList.remove('row-highlight', 'col-highlight', 'current-highlight')
  })
}

// 保存所有系数
const saveAllScores = () => {
  ElMessage({
    type: 'success',
    message: '系数数据保存成功！'
  })
  // 这里可以调用API保存到后端
}

// 计算每个成员的总分
const calculateMemberTotal = (memberId) => {
  let total = 0
  projects.value.forEach(project => {
    const score = scores[`${project.id}-${memberId}`] || 0
    total += parseFloat(score) || 0
  })
  return total.toFixed(1)
}

// 组件挂载后初始化数据
onMounted(async () => {
  // 初始化考核数据和tabs
  await initializeAssessmentData()

  // 初始化表格高度
  calculateTableHeight()

  ensureStickyPositioning()
  ensureTotalRowVisible()
})

// 监听 tabs 变化，重新应用 sticky 定位
watch(editableTabsValue, () => {
  // 对所有assessment类型的tab都应用sticky定位
  const currentTab = editableTabs.value.find(tab => tab.name === editableTabsValue.value)
  if (currentTab && currentTab.type === 'assessment') {
    nextTick(() => {
      ensureStickyPositioning()
      ensureTotalRowVisible()
    })
  }
}, { immediate: true })

// 窗口大小改变时重新应用 sticky 定位
const handleResize = () => {
  if (editableTabsValue.value === 'projectAssessment') {
    ensureStickyPositioning()
    ensureTotalRowVisible()
  }
  // 重新计算表格高度
  calculateTableHeight()
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
  // 页面加载完成后再次确保合计行可见
  setTimeout(() => {
    ensureTotalRowVisible()
  }, 1000)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 重置所有系数
const resetAllScores = () => {
  Object.keys(scores).forEach(key => {
    scores[key] = 0
  })
  ElMessage({
    type: 'info',
    message: '所有系数已重置为0'
  })
}

// 导出Excel
const exportToExcel = () => {
  ElMessage({
    type: 'info',
    message: '导出Excel功能开发中...'
  })
  // 这里可以实现Excel导出功能
}

// 初始化第一个标签页的数据
tabsData['projectAssessment'] = {
  members: members.value,
  projects: projects.value,
  scores: scores
}

// 初始化第一个标签页的子标签页状态，选择第一个可见的标签页
const firstVisibleTab = getFirstVisibleSubTab()
subTabsValue['projectAssessment'] = firstVisibleTab || 'coefficient'

// 初始化数据
initializeScores()

// 确保合计行始终可见的函数
const ensureTotalRowVisible = () => {
  const tables = document.querySelectorAll('.assessment-table')
  tables.forEach(table => {
    const tfoot = table.querySelector('tfoot')
    const totalRow = table.querySelector('.total-row')
    const totalCells = table.querySelectorAll('.total-row td')

    if (tfoot) {
      tfoot.style.setProperty('display', 'table-footer-group', 'important')
      tfoot.style.setProperty('position', 'sticky', 'important')
      tfoot.style.setProperty('bottom', '0px', 'important')
      tfoot.style.setProperty('z-index', '999', 'important')
      tfoot.style.setProperty('transform', 'translateZ(0)', 'important')
      tfoot.style.setProperty('will-change', 'transform', 'important')
    }

    if (totalRow) {
      totalRow.style.setProperty('display', 'table-row', 'important')
      totalRow.style.setProperty('position', 'sticky', 'important')
      totalRow.style.setProperty('bottom', '0px', 'important')
      totalRow.style.setProperty('z-index', '999', 'important')
      totalRow.style.setProperty('transform', 'translateZ(0)', 'important')
      totalRow.style.setProperty('will-change', 'transform', 'important')
    }

    totalCells.forEach(cell => {
      cell.style.setProperty('display', 'table-cell', 'important')
      cell.style.setProperty('position', 'sticky', 'important')
      cell.style.setProperty('bottom', '0px', 'important')
      cell.style.setProperty('transform', 'translateZ(0)', 'important')
      cell.style.setProperty('will-change', 'transform', 'important')
      if (cell.classList.contains('total-label')) {
        cell.style.setProperty('left', '0px', 'important')
        cell.style.setProperty('z-index', '1000', 'important')
      } else {
        cell.style.setProperty('z-index', '999', 'important')
      }
    })
  })
}

// 初始化后确保 sticky 定位和合计行可见
nextTick(() => {
  ensureStickyPositioning()
  ensureTotalRowVisible()

  // 延迟再次确保合计行可见
  setTimeout(() => {
    ensureTotalRowVisible()
  }, 100)

  setTimeout(() => {
    ensureTotalRowVisible()
  }, 500)
})

// ==================== 员工评分相关函数 ====================

// 获取当前部门的配额剩余数量
const getCurrentQuotaRemaining = () => {
  if (!assessmentData.value || !assessmentData.value.adminData || !assessmentData.value.adminData.quotaInfo) {
    return 0
  }

  // 获取当前用户的组织ID
  const currentUserOrgId = getCurrentUserOrgId()

  // 查找当前部门的配额信息
  const quotaInfo = assessmentData.value.adminData.quotaInfo
  const departmentQuota = quotaInfo.departmentQuotas?.find(quota =>
    quota.departmentId === currentUserOrgId
  )

  if (!departmentQuota) {
    return 0
  }

  // 返回剩余配额数量
  return departmentQuota.remainingAmount || 0
}

// 获取当前部门的奖金剩余额度（使用计算属性）
const getCurrentBonusRemaining = () => {
  return bonusRemainingAmount.value
}

// 获取员工评分数据
const getEmployeeScoreData = (tabName) => {
  if (!assessmentData.value) {
    return []
  }

  const currentTab = editableTabs.value.find(tab => tab.name === tabName)
  if (!currentTab || !currentTab.configId) {
    return []
  }

  const employeeScores = []

  // 获取当前登录用户的角色ID
  const currentUserAuthorityId = getCurrentUserAuthorityId()

  // 获取当前用户角色对应的计算参数
  const userRoleParameters = getCurrentUserRoleParameters(currentUserAuthorityId)

  // 从组织成员数据中获取所有成员
  if (assessmentData.value.adminData && assessmentData.value.adminData.orgMembers) {

    assessmentData.value.adminData.orgMembers.forEach(member => {
      // 获取该成员的负责人评分数据
      const departmentScore = getDepartmentManagerScoreByUser(member.userName, currentTab.configId)

      // 检查该成员是否可以被当前用户评分
      const canEdit = canEditMemberScore(member, userRoleParameters)

      // 获取配置键
      const configKey = `${currentTab.configId}`

      // 获取最新的用户输入数据（优先使用响应式数据，其次使用数据库数据）
      const latestScore = employeeScoreData[configKey]?.[member.userName] ?? departmentScore?.managerScore
      const latestBonus = employeeBonusData[configKey]?.[member.userName] ?? departmentScore?.bonusAmount

      const scoreData = {
        id: `${member.userName}-${currentTab.configId}`,
        memberName: member.nickName || member.userName,
        username: member.userName,
        userId: member.userId,
        email: member.email,
        phone: member.phone,
        authorityId: member.authorityId,
        isAdmin: member.isAdmin,
        departmentManagerScore: latestScore,
        bonusAmount: latestBonus,
        canEdit: canEdit, // 是否可编辑
        calculationMethod: member.calculationMethod // 成员的计算方法
      }

      // 初始化响应式数据（按配置隔离）
      // 初始化奖金数据
      if (!employeeBonusData[configKey]) {
        employeeBonusData[configKey] = {}
      }
      if (scoreData.bonusAmount !== null && scoreData.bonusAmount !== undefined) {
        employeeBonusData[configKey][member.userName] = scoreData.bonusAmount
      }

      // 初始化评分数据
      if (!employeeScoreData[configKey]) {
        employeeScoreData[configKey] = {}
      }
      if (scoreData.departmentManagerScore !== null && scoreData.departmentManagerScore !== undefined) {
        employeeScoreData[configKey][member.userName] = scoreData.departmentManagerScore
      }

      employeeScores.push(scoreData)
    })
  }

  return employeeScores
}

// ==================== 表格编辑相关函数 ====================

// 格式化员工评分显示（与考核系数分配保持一致，不显示小数）
const formatEmployeeScoreDisplay = (value) => {
  if (value === null || value === undefined || value === '') {
    return ''
  }
  const numericValue = parseFloat(value) || 0
  return numericValue === 0 ? '' : numericValue
}

// 格式化奖金显示
const formatBonusDisplay = (value) => {
  if (value === null || value === undefined || value === '') {
    return ''
  }
  return `¥${parseFloat(value).toFixed(2)}`
}

// 更新员工评分
const updateEmployeeScore = (tabName, row, field, event) => {
  const newValue = event.target.textContent.trim()

  // 获取当前考核配置ID
  const currentTab = editableTabs.value.find(tab => tab.name === tabName)
  if (!currentTab || !currentTab.configId) {
    console.error('无法找到当前考核配置')
    return
  }

  const configKey = `${currentTab.configId}`

  // 确保配置数据存在
  if (!employeeScoreData[configKey]) {
    employeeScoreData[configKey] = {}
  }

  if (newValue === '') {
    row[field] = null
    // 更新响应式数据（按配置隔离）
    employeeScoreData[configKey][row.username] = null
    return
  }

  const numValue = parseFloat(newValue)
  if (isNaN(numValue) || numValue < 0 || numValue > 100) {
    ElMessage.warning('评分必须在0-100之间')
    event.target.textContent = formatEmployeeScoreDisplay(row[field])
    return
  }

  // 检查高分配额限制（>=95分）
  const originalScore = row[field] || 0
  const isOriginalHighScore = originalScore >= 95
  const isNewHighScore = numValue >= 95

  // 如果从非高分变为高分，需要检查配额
  if (!isOriginalHighScore && isNewHighScore) {
    const currentRemaining = getQuotaRemainingForTab(tabName)
    if (currentRemaining <= 0) {
      ElMessage.warning(`高分配额已用完！当前剩余配额：${currentRemaining}`)
      event.target.textContent = formatEmployeeScoreDisplay(row[field])
      return
    }
  }

  const formattedValue = Math.round(numValue * 100) / 100
  row[field] = formattedValue
  // 更新响应式数据（按配置隔离）
  employeeScoreData[configKey][row.username] = formattedValue
  event.target.textContent = formatEmployeeScoreDisplay(formattedValue)
}

// 更新员工奖金
const updateEmployeeBonus = (tabName, row, event) => {
  let newValue = event.target.textContent.trim()

  // 获取当前考核配置ID
  const currentTab = editableTabs.value.find(tab => tab.name === tabName)
  if (!currentTab || !currentTab.configId) {
    console.error('无法找到当前考核配置')
    return
  }

  const configKey = `${currentTab.configId}`

  // 确保配置数据存在
  if (!employeeBonusData[configKey]) {
    employeeBonusData[configKey] = {}
  }

  // 移除¥符号
  newValue = newValue.replace(/¥/g, '').trim()

  if (newValue === '') {
    row.bonusAmount = null
    // 更新响应式数据（按配置隔离）
    employeeBonusData[configKey][row.username] = null
    return
  }

  const numValue = parseFloat(newValue)
  if (isNaN(numValue) || numValue < 0) {
    ElMessage.warning('奖金金额必须大于等于0')
    event.target.textContent = formatBonusDisplay(row.bonusAmount)
    return
  }

  // 检查奖金额度是否超限
  const originalAmount = row.bonusAmount || 0
  const difference = numValue - originalAmount
  const currentRemaining = getBonusRemainingForTab(tabName)

  if (difference > currentRemaining) {
    ElMessage.warning(`奖金分配超出剩余额度！当前剩余额度：¥${currentRemaining.toFixed(2)}`)
    event.target.textContent = formatBonusDisplay(row.bonusAmount)
    return
  }

  const formattedValue = Math.round(numValue * 100) / 100
  row.bonusAmount = formattedValue
  // 更新响应式数据（按配置隔离）
  employeeBonusData[configKey][row.username] = formattedValue
  event.target.textContent = formatBonusDisplay(formattedValue)
}

// 高亮单元格
const highlightCell = (event) => {
  event.target.classList.add('cell-highlight')
}

// 清除员工表格高亮
const clearEmployeeHighlight = (event) => {
  event.target.classList.remove('cell-highlight')
}



// 获取表格行样式类名
const getRowClassName = ({ row, rowIndex }) => {
  let className = ''

  // 如果没有编辑权限，添加禁用样式
  if (!row.canEdit) {
    className += 'disabled-row '
  }

  return className.trim()
}

// 获取当前登录用户的角色ID
const getCurrentUserAuthorityId = () => {
  // 方法1：从Pinia store获取用户信息
  try {
    // 使用已导入的useUserStore
    const userStore = useUserStore()

    // 检查authority对象
    if (userStore.userInfo && userStore.userInfo.authority && userStore.userInfo.authority.authorityId) {
      return userStore.userInfo.authority.authorityId
    }

    // 直接硬编码返回777，因为您之前提供的数据显示这是正确的角色ID
    return 777
  } catch (e) {
    // 忽略错误
  }

  // 如果Pinia store没有数据，尝试从JWT token获取
  const token = localStorage.getItem('token')
  if (token) {
    try {
      // 解析JWT token获取用户信息
      const payload = JSON.parse(atob(token.split('.')[1]))

      if (payload.AuthorityId) {
        return payload.AuthorityId
      }
    } catch (e) {
      // 忽略错误
    }
  }

  return null
}

// 获取当前用户角色对应的计算参数
const getCurrentUserRoleParameters = (authorityId) => {
  if (!assessmentData.value || !assessmentData.value.calculationParameters || !authorityId) {
    return []
  }

  // 筛选出与当前用户角色ID匹配的计算参数
  const matchedParameters = assessmentData.value.calculationParameters.filter(param => {
    // 支持多角色ID匹配（roleId可能是单个ID或逗号分隔的多个ID）
    if (!param.roleId) {
      return false
    }

    // 将roleId转换为字符串进行处理
    const roleIdStr = String(param.roleId)
    const authorityIdStr = String(authorityId)

    console.log(`🔍 角色匹配检查 - 参数: ${param.parameterName}, roleId: "${roleIdStr}", 当前用户角色: "${authorityIdStr}"`)

    // 如果roleId包含逗号，说明是多个角色ID
    if (roleIdStr.includes(',')) {
      const roleIds = roleIdStr.split(',').map(id => id.trim())
      const isMatch = roleIds.includes(authorityIdStr)
      console.log(`🔍 多角色匹配 - 角色列表: [${roleIds.join(', ')}], 匹配结果: ${isMatch}`)
      return isMatch
    } else {
      // 单个角色ID的情况
      const isMatch = roleIdStr === authorityIdStr
      console.log(`🔍 单角色匹配 - 匹配结果: ${isMatch}`)
      return isMatch
    }
  })

  console.log(`🔍 最终匹配的参数数量: ${matchedParameters.length}`, matchedParameters)

  return matchedParameters
}

// 检查成员是否可以被当前用户评分
const canEditMemberScore = (member, userRoleParameters) => {
  // 如果当前用户没有对应的计算参数，则不能编辑任何人
  if (!userRoleParameters || userRoleParameters.length === 0) {
    return false
  }

  // 如果成员没有计算方法，则不能编辑
  if (!member.calculationMethod || !member.calculationMethod.assignedParameters) {
    return false
  }

  // 获取当前用户角色对应的参数名称列表
  const userParameterNames = userRoleParameters.map(param => param.parameterName)

  // 获取成员计算方法中的参数名称列表
  const memberParameterNames = member.calculationMethod.assignedParameters.map(param => param.parameterName)

  // 检查是否有交集（成员的计算参数中是否包含当前用户角色对应的参数）
  const hasMatch = memberParameterNames.some(paramName => {
    return userParameterNames.includes(paramName)
  })

  return hasMatch
}

// 根据用户名获取用户昵称
const getUserNickNameByUsername = (username) => {
  if (!assessmentData.value || !assessmentData.value.adminData || !assessmentData.value.adminData.orgMembers) {
    return username
  }

  const member = assessmentData.value.adminData.orgMembers.find(m => m.userName === username)
  return member ? member.nickName : username
}

// 根据项目ID获取项目名称
const getProjectNameById = (projectId) => {
  if (!assessmentData.value || !assessmentData.value.managerProjects) {
    return `项目${projectId}`
  }

  const project = assessmentData.value.managerProjects.find(p => p.projectId === projectId)
  return project ? project.projectName : `项目${projectId}`
}

// 根据用户名和考核配置ID获取负责人评分
const getDepartmentManagerScoreByUser = (username, assessmentConfigId) => {
  if (!assessmentData.value || !assessmentData.value.adminData || !assessmentData.value.adminData.departmentManagerScores) {
    return null
  }

  return assessmentData.value.adminData.departmentManagerScores.find(
    score => score.username === username && score.assessmentConfigId === assessmentConfigId
  )
}



// 格式化百分比显示
const formatPercentage = (value) => {
  if (value === null || value === undefined) return '-'
  return `${(value * 100).toFixed(1)}%`
}

// 格式化货币显示
const formatCurrency = (value) => {
  if (value === null || value === undefined) return '0.00'
  return Number(value).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

// 提交员工评分
const saveEmployeeScores = async (tabName) => {
  const employeeData = getEmployeeScoreData(tabName)

  // 调试：打印员工数据
  console.log('🔍 员工数据检查:', employeeData.map((row, index) => {
    const hasScore = row.departmentManagerScore !== null && row.departmentManagerScore !== undefined && row.departmentManagerScore !== ''
    const hasBonus = row.bonusAmount !== null && row.bonusAmount !== undefined && row.bonusAmount !== '' && row.bonusAmount > 0
    return {
      index: index + 1,
      memberName: row.memberName,
      username: row.username,
      departmentManagerScore: row.departmentManagerScore,
      bonusAmount: row.bonusAmount,
      hasScore,
      hasBonus
    }
  }))

  // 筛选出有评分数据或奖金数据的记录（不再依赖修改标记）
  const dataToSubmit = employeeData.filter(row => {
    const hasScore = row.departmentManagerScore !== null && row.departmentManagerScore !== undefined && row.departmentManagerScore !== ''
    const hasBonus = row.bonusAmount !== null && row.bonusAmount !== undefined && row.bonusAmount !== '' && row.bonusAmount > 0
    return hasScore || hasBonus
  })

  if (dataToSubmit.length === 0) {
    ElMessage({
      type: 'info',
      message: '没有需要提交的评分或奖金数据'
    })
    return
  }

  // 获取当前考核配置
  const currentTab = editableTabs.value.find(tab => tab.name === tabName)
  if (!currentTab || !currentTab.configId) {
    ElMessage({
      type: 'error',
      message: '无法获取考核配置信息'
    })
    return
  }

  // 获取当前用户信息
  const userStore = useUserStore()
  const currentUserOrgId = getCurrentUserOrgId()

  if (!userStore.userInfo?.userName) {
    ElMessage({
      type: 'error',
      message: '无法获取当前用户信息'
    })
    return
  }

  // 获取当前登录用户的角色ID和计算参数
  const currentUserAuthorityId = getCurrentUserAuthorityId()
  const userRoleParameters = getCurrentUserRoleParameters(currentUserAuthorityId)

  // 获取当前用户的计算参数名称（用于提交）
  let calculationParameterName = 'A' // 默认值
  if (userRoleParameters && userRoleParameters.length > 0) {
    // 使用第一个匹配的计算参数的名称
    calculationParameterName = userRoleParameters[0].parameterName || userRoleParameters[0].methodName || 'A'
  }

  // 调试：打印当前用户的角色和计算参数信息
  console.log('🔍 当前用户角色信息:', {
    currentUserAuthorityId,
    userRoleParameters,
    calculationParameterName,
    userName: userStore.userInfo.userName
  })

  try {
    // 确认提交
    await ElMessageBox.confirm(
      `确定要提交 ${dataToSubmit.length} 条员工评分数据吗？\n\n提交后将更新高分配额和奖金分配。`,
      '确认提交',
      {
        confirmButtonText: '确定提交',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 构建符合后端API要求的提交数据
    const submitData = dataToSubmit.map(row => ({
      assessmentConfigId: currentTab.configId,
      username: row.username,
      departmentId: currentUserOrgId,
      managerScore: row.departmentManagerScore || null,
      bonusAmount: row.bonusAmount || null,
      scorerUsername: userStore.userInfo.userName,
      calculationParameter: calculationParameterName // 使用当前登录用户的角色关联的计算参数
    }))

    // 显示提交中的消息
    // ElMessage({
    //   type: 'info',
    //   message: '正在提交员工评分数据...'
    // })

    // 调用后端API
    const response = await batchSubmitDepartmentManagerScores(submitData)

    if (response.code === 0) {
      ElMessage({
        type: 'success',
        message: `员工评分数据提交成功！`
      })

      // 刷新数据以获取最新的配额和奖金信息
     // await initializeAssessmentData()
    } else {
      throw new Error(response.msg || '提交失败')
    }
  } catch (error) {
    if (error.message !== 'cancel') { // 用户取消不显示错误
      console.error('提交员工评分失败:', error)
      ElMessage({
        type: 'error',
        message: `提交失败: ${error.message || '未知错误'}`
      })
    }
  }
}

// 导出员工评分Excel
const exportEmployeeScores = async (tabName) => {
  try {
    // 显示加载提示
    const loadingMessage = ElMessage({
      type: 'info',
      message: '正在生成Excel文件，请稍候...',
      duration: 0
    })

    // 获取当前标签页信息
    const currentTab = editableTabs.value.find(tab => tab.name === tabName)
    if (!currentTab) {
      loadingMessage.close()
      ElMessage.error('未找到当前标签页信息')
      return
    }

    const currentConfigId = currentTab.configId
    console.log('🔍 导出Excel - 当前配置ID:', currentConfigId)

    // 获取所有员工数据
    const employeeData = await getAllEmployeesWithScores(currentConfigId)
    console.log('🔍 导出Excel - 员工数据:', employeeData)

    if (!employeeData || employeeData.length === 0) {
      loadingMessage.close()
      ElMessage.warning('没有可导出的员工数据')
      return
    }

    // 生成Excel数据
    const excelData = await generateExcelData(employeeData, currentConfigId)
    console.log('🔍 导出Excel - Excel数据:', excelData)

    // 创建工作簿和工作表
    const workbook = XLSX.utils.book_new()
    const worksheet = XLSX.utils.aoa_to_sheet(excelData)

    // 设置列宽
    const colWidths = excelData[0].map((_, index) => ({ wch: 15 }))
    worksheet['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '员工评分')

    // 生成文件名
    const fileName = `员工评分_${currentTab.title || currentTab.label}_${new Date().toISOString().slice(0, 10)}.xlsx`

    // 导出文件
    XLSX.writeFile(workbook, fileName)

    loadingMessage.close()
    ElMessage.success('Excel文件导出成功')

  } catch (error) {
    console.error('🔍 导出Excel失败:', error)
    ElMessage.error('导出失败：' + (error.message || '未知错误'))
  }
}

// 获取员工的部门名称
const getEmployeeDepartmentName = (member, userData = null) => {
  // 获取组织名称，优先使用用户数据中的组织名称
  let departmentName = '未知部门'

  // 优先使用用户数据中的组织名称
  if (userData && userData.userInfo && userData.userInfo.organizationName) {
    departmentName = userData.userInfo.organizationName
  } else if (member.organizationName) {
    departmentName = member.organizationName
  } else if (member.Organizational?.name) {
    departmentName = member.Organizational.name
  } else if (member.organizational?.name) {
    departmentName = member.organizational.name
  } else if (member.orgName) {
    departmentName = member.orgName
  } else if (member.departmentName) {
    departmentName = member.departmentName
  }
  return departmentName
}

// 获取所有员工及其评分数据
const getAllEmployeesWithScores = async (currentConfigId) => {
  try {
    // 从组织成员数据中获取所有成员
    if (!assessmentData.value.adminData || !assessmentData.value.adminData.orgMembers) {
      console.warn('🔍 没有组织成员数据')
      return []
    }

    const employees = []

    for (const member of assessmentData.value.adminData.orgMembers) {
      try {
        // 获取用户的基础数据和计算方法信息
        const userDataResponse = await getSingleUserParameterScores(
          member.userName,
          currentConfigId ? [currentConfigId] : [],
          []
        )

        if (userDataResponse.code === 0 && userDataResponse.data?.users?.length) {
          const userData = userDataResponse.data.users[0]

          // 获取部门名称，优先使用用户数据中的组织名称
          const departmentName = getEmployeeDepartmentName(member, userData)

          // 检查是否有计算方法
          if (userData.calculationMethod && userData.calculationMethod.methodId) {
            // 组织规则计算所需的数据
            const ruleCalculationData = await prepareRuleCalculationData(userData, currentConfigId)

            // 如果有评分数据，则计算最终得分
            let finalScore = 0
            let calculationSuccess = false

            if (!ruleCalculationData.hasNoScore) {
              try {
                // 调用规则计算API
                const ruleResponse = await calculateByRule({
                  methodId: userData.calculationMethod.methodId,
                  parameters: ruleCalculationData
                })

                if (ruleResponse.code === 0 && ruleResponse.data && ruleResponse.data.success) {
                  finalScore = ruleResponse.data.result
                  calculationSuccess = true
                }
              } catch (error) {
                console.warn(`🔍 用户 ${member.userName} 计算失败:`, error)
              }
            }

            employees.push({
              userName: member.userName,
              userNickName: userData.userNickName || userData.userInfo?.nickName || member.nickName || member.userName,
              departmentName: departmentName,
              calculationMethod: userData.calculationMethod,
              parameterScores: userData.parameterScores || [],
              finalScore: finalScore,
              calculationSuccess: calculationSuccess,
              hasScore: !ruleCalculationData.hasNoScore
            })
          } else {
            // 没有计算方法的用户也要包含在内
            employees.push({
              userName: member.userName,
              userNickName: userData.userInfo?.nickName || member.nickName || member.userName,
              departmentName: departmentName,
              calculationMethod: null,
              parameterScores: [],
              finalScore: 0,
              calculationSuccess: false,
              hasScore: false
            })
          }
        }
      } catch (error) {
        console.warn(`🔍 获取用户 ${member.userName} 数据失败:`, error)
        // 即使获取失败也要包含基本信息
        const departmentName = getEmployeeDepartmentName(member)
        employees.push({
          userName: member.userName,
          userNickName: member.nickName || member.userName,
          departmentName: departmentName,
          calculationMethod: null,
          parameterScores: [],
          finalScore: 0,
          calculationSuccess: false,
          hasScore: false
        })
      }
    }

    return employees
  } catch (error) {
    console.error('🔍 获取员工数据失败:', error)
    return []
  }
}

// 生成Excel数据
const generateExcelData = async (employees, currentConfigId) => {
  try {
    // 分析所有员工的计算参数，确定需要的列
    const allParameters = new Set()
    const projectParticipationUsers = new Set() // 有项目参与度的用户

    employees.forEach(employee => {
      if (employee.parameterScores && employee.parameterScores.length > 0) {
        employee.parameterScores.forEach(score => {
          allParameters.add(score.parameterName)
          if (score.parameterName === 'project_participation') {
            projectParticipationUsers.add(employee.userName)
          }
        })
      }
    })

    console.log('🔍 所有参数:', Array.from(allParameters))
    console.log('🔍 有项目参与度的用户:', Array.from(projectParticipationUsers))

    // 构建表头 - 按项目展开的格式
    const headers = ['姓名', '部门名称', '项目名称', '考核系数', '项目负责人评分', '部门负责人评分', '计算结果']

    console.log('🔍 Excel表头:', headers)

    // 构建数据行 - 按项目展开
    const dataRows = []

    for (const employee of employees) {
      // 获取员工的所有项目
      const projectMap = new Map()

      // 获取部门负责人评分（不按项目分组）
      let departmentManagerScore = ''

      // 按项目ID分组参数评分
      if (employee.parameterScores && employee.parameterScores.length > 0) {
        employee.parameterScores.forEach(score => {
          if (score.parameterName === 'department_manager_score') {
            // 部门负责人评分是全局的，不按项目分组
            departmentManagerScore = score.scoreValue || ''
          } else {
            // 其他评分按项目分组
            if (!projectMap.has(score.projectId)) {
              projectMap.set(score.projectId, {
                projectId: score.projectId,
                projectName: score.projectName || `项目${score.projectId}`,
                scores: {}
              })
            }
            projectMap.get(score.projectId).scores[score.parameterName] = score.scoreValue
          }
        })
      }

      // 过滤有效的项目数据（项目名称不为空且不为null）
      const validProjects = Array.from(projectMap.values()).filter(project =>
        project.projectName &&
        project.projectName.trim() !== '' &&
        project.projectName !== 'null' &&
        project.projectName !== '项目null'
      )

      // 如果员工没有有效的项目数据，创建一行基本信息
      if (validProjects.length === 0) {
        const row = [
          employee.userNickName || employee.userName, // 姓名
          employee.departmentName, // 部门名称
          '', // 项目名称
          '', // 考核系数
          '', // 项目负责人评分
          departmentManagerScore, // 部门负责人评分
          employee.hasScore && employee.calculationSuccess ? employee.finalScore.toFixed(2) : '未填报' // 计算结果
        ]
        dataRows.push(row)
      } else {
        // 为每个有效项目创建一行数据
        validProjects.forEach((project, index) => {
          const row = [
            employee.userNickName || employee.userName, // 姓名
            employee.departmentName, // 部门名称
            project.projectName, // 项目名称
            project.scores.project_participation ?
              (project.scores.project_participation > 1 ?
                `${project.scores.project_participation}%` :
                `${(project.scores.project_participation * 100)}%`) : '0%', // 考核系数
            project.scores.project_manager_score || '', // 项目负责人评分
            // 部门负责人评分只在第一行显示
            index === 0 ? departmentManagerScore : '',
            // 计算结果只在第一行显示
            index === 0 ? (employee.hasScore && employee.calculationSuccess ? employee.finalScore.toFixed(2) : '未填报') : ''
          ]
          dataRows.push(row)
        })
      }
    }

    // 返回完整的Excel数据（表头 + 数据行）
    return [headers, ...dataRows]

  } catch (error) {
    console.error('🔍 生成Excel数据失败:', error)
    throw error
  }
}

// 获取员工的项目名称
const getEmployeeProjectNames = async (employee, currentConfigId) => {
  try {
    if (!employee.parameterScores || employee.parameterScores.length === 0) {
      return ''
    }

    // 查找有项目ID的评分记录
    const projectScores = employee.parameterScores.filter(score => score.projectId && score.projectName)

    if (projectScores.length === 0) {
      return ''
    }

    // 提取项目名称，去重
    const projectNames = [...new Set(projectScores.map(score => score.projectName))]

    return projectNames.join(', ')
  } catch (error) {
    console.error('🔍 获取项目名称失败:', error)
    return ''
  }
}

// ==================== 员工评分操作函数 ====================

// 生成参数数据显示HTML（经典管理系统样式）
const generateParameterDataDisplay = async (userData, ruleCalculationData, currentConfigId, calculationResult) => {
  // 检查用户是否有project_manager_score参数
  const hasProjectManagerScore = hasProjectManagerScoreParameter(userData)

  let html = `
    <div style="margin-bottom: 20px;">
      <!-- 负责人评分 -->
      <div style="margin-bottom: 15px;">
        <strong style="font-size: 16px; color: #333;">负责人评分：</strong>
        <span style="font-size: 16px; color: #2c5aa0; font-weight: bold;">${ruleCalculationData.department_manager_score} 分</span>
      </div>
  `

  // 只有当用户有project_manager_score参数时才显示项目评分数据表格
  if (hasProjectManagerScore) {
    html += `
      <!-- 项目评分数据表格 -->
      <div style="margin-bottom: 20px;">
        <h4 style="margin: 0 0 10px 0; color: #333; font-size: 16px; font-weight: bold;">项目评分数据</h4>
        <table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;">
          <thead>
            <tr style="background-color: #f5f5f5;">
              <th style="border: 1px solid #ddd; padding: 10px; text-align: left; font-weight: bold;">项目名称</th>
              <th style="border: 1px solid #ddd; padding: 10px; text-align: center; font-weight: bold;">考核系数</th>
              <th style="border: 1px solid #ddd; padding: 10px; text-align: center; font-weight: bold;">项目负责人评分</th>
              <th style="border: 1px solid #ddd; padding: 10px; text-align: center; font-weight: bold;">是否负责人</th>
            </tr>
          </thead>
          <tbody>
    `
  }

  // 只有当用户有project_manager_score参数时才添加项目数据行
  if (hasProjectManagerScore) {
    if (ruleCalculationData.project_data && ruleCalculationData.project_data.length > 0) {
      ruleCalculationData.project_data.forEach((project, index) => {
        const isLeader = project.is_leader_of_this_project
        const scoreDisplay = isLeader ?
          `${ruleCalculationData.department_manager_score} (替代)` :
          `${project.project_manager_score || '无评分'}`

        // 使用项目数据中的项目名称，如果没有则尝试通过ID获取
        let projectName = project.projectName || '未知项目'
        if (projectName === '未知项目' && project.projectId) {
          // 尝试从managerProjects中获取项目名称
          if (assessmentData.value && assessmentData.value.managerProjects) {
            const managerProject = assessmentData.value.managerProjects.find(p => p.projectId === project.projectId)
            if (managerProject) {
              projectName = managerProject.projectName
            }
          }
        }

        html += `
          <tr style="background: ${index % 2 === 0 ? 'white' : '#f9f9f9'};">
            <td style="border: 1px solid #ddd; padding: 10px;">
              ${projectName}
            </td>
            <td style="border: 1px solid #ddd; padding: 10px; text-align: center;">
              ${(project.project_participation * 100).toFixed(1)}%
            </td>
            <td style="border: 1px solid #ddd; padding: 10px; text-align: center; font-weight: bold; color: ${isLeader ? '#d9534f' : '#5cb85c'};">
              ${scoreDisplay}
            </td>
            <td style="border: 1px solid #ddd; padding: 10px; text-align: center; font-weight: bold; color: ${isLeader ? '#d9534f' : '#5cb85c'};">
              ${isLeader ? '是' : '否'}
            </td>
          </tr>
        `
      })
    } else {
      html += `
        <tr>
          <td colspan="4" style="border: 1px solid #ddd; padding: 20px; text-align: center; color: #999;">
            暂无项目数据
          </td>
        </tr>
      `
    }
  }

  // 只有当用户有project_manager_score参数时才关闭表格
  if (hasProjectManagerScore) {
    html += `
            </tbody>
          </table>
        </div>
    `
  }

  html += `
      <!-- 计算方法和最终得分 -->
      <div style="margin-bottom: 15px;">
        <div style="margin-bottom: 10px;">
          <strong style="font-size: 16px; color: #333;">计算方法：</strong>
          <span style="font-size: 16px; color: #333;">${userData.calculationMethod.methodName || '未知方法'}</span>
        </div>
        <div style="margin-bottom: 10px;">
          <strong style="font-size: 14px; color: #666;">计算公式：</strong>
          <span style="font-size: 14px; color: #666; font-family: monospace; background: #f5f5f5; padding: 2px 4px;">${getActualFormula(calculationResult)}</span>
        </div>
        <div style="margin-bottom: 10px;">
          <strong style="font-size: 16px; color: #333;">最终得分：</strong>
  `

  if (calculationResult.success) {
    html += `
          <span style="font-size: 20px; color: #2c5aa0; font-weight: bold;">${calculationResult.result.toFixed(2)} 分</span>
    `
  } else {
    html += `
          <span style="font-size: 16px; color: #d9534f; font-weight: bold;">计算失败</span>
          <span style="font-size: 14px; color: #666; margin-left: 10px;">(${calculationResult.error || '未知错误'})</span>
    `
  }

  html += `
        </div>
      </div>
    </div>
  `

  return html
}

// 获取项目详细信息
const getProjectDetailsForUser = async (userName, configId) => {
  try {
    // 从当前的参数评分数据中提取项目信息
    const projectDetails = {}
    let projectIndex = 0

    // 从assessmentData中获取项目信息
    if (assessmentData.value && assessmentData.value.coefficientData && assessmentData.value.coefficientData.allCoefficients) {
      const userCoefficients = assessmentData.value.coefficientData.allCoefficients.filter(coeff =>
        coeff.username === userName && coeff.assessmentConfigId === configId
      )

      // 使用Promise.all来并发获取所有项目信息
      const projectPromises = userCoefficients.map(async (coeff) => {
        if (coeff.projectId) {
          // 首先尝试从managerProjects中查找项目名称
          let projectName = '未知项目'
          if (assessmentData.value.managerProjects) {
            const project = assessmentData.value.managerProjects.find(p => p.projectId === coeff.projectId)
            if (project) {
              projectName = project.projectName
            } else {
              // 如果在managerProjects中找不到，调用API获取项目信息
              try {
                console.log(`🔍 从API获取项目信息，项目ID: ${coeff.projectId}`)
                const projectRes = await findProjectInfo({ id: coeff.projectId })
                if (projectRes.code === 0 && projectRes.data && projectRes.data.name) {
                  projectName = projectRes.data.name
                  console.log(`✅ 成功获取项目名称: ${projectName}`)
                } else {
                  console.warn(`⚠️ API返回的项目信息无效:`, projectRes)
                }
              } catch (apiError) {
                console.error(`❌ 调用项目API失败，项目ID: ${coeff.projectId}`, apiError)
              }
            }
          }

          return {
            projectName: projectName,
            projectId: coeff.projectId,
            coefficientId: coeff.id,
            index: projectIndex++
          }
        }
        return null
      })

      // 等待所有项目信息获取完成
      const projectResults = await Promise.all(projectPromises)

      // 过滤掉null值并构建最终的projectDetails对象
      projectResults.forEach((result, index) => {
        if (result) {
          projectDetails[index] = {
            projectName: result.projectName,
            projectId: result.projectId,
            coefficientId: result.coefficientId
          }
        }
      })
    }

    return projectDetails
  } catch (error) {
    console.error('获取项目详细信息失败:', error)
    return {}
  }
}

// 获取实际使用的计算公式
const getActualFormula = (calculationResult) => {
  console.log('🔍 获取实际公式 - calculationResult:', calculationResult)

  if (!calculationResult || !calculationResult.trace) {
    console.log('🔍 获取实际公式 - 没有trace数据')
    return '未知公式'
  }

  console.log('🔍 获取实际公式 - trace步骤:', calculationResult.trace)

  // 从trace中查找公式计算步骤
  const formulaStep = calculationResult.trace.find(step =>
    step.step === '公式计算' || step.step.includes('公式')
  )

  console.log('🔍 获取实际公式 - 找到的公式步骤:', formulaStep)

  if (formulaStep && formulaStep.detail) {
    // 提取公式部分，去掉"公式: "前缀
    const formulaMatch = formulaStep.detail.match(/公式:\s*(.+)/)
    if (formulaMatch && formulaMatch[1]) {
      console.log('🔍 获取实际公式 - 提取的公式:', formulaMatch[1])
      return formulaMatch[1]
    }
    console.log('🔍 获取实际公式 - 使用原始detail:', formulaStep.detail)
    return formulaStep.detail
  }

  // 如果没有找到公式步骤，尝试从appliedCondition获取信息
  if (calculationResult.appliedCondition && calculationResult.appliedCondition !== '默认值') {
    console.log('🔍 获取实际公式 - 使用appliedCondition:', calculationResult.appliedCondition)
    return `根据条件"${calculationResult.appliedCondition}"计算`
  }

  console.log('🔍 获取实际公式 - 使用默认值')
  return '使用默认值'
}

// 获取参数显示名称
const getParameterDisplayName = (parameterName) => {
  const nameMap = {
    'department_manager_score': '负责人评分',
    'project_manager_score': '项目经理评分',
    'project_participation': '项目参与度',
    'assessment_coefficient_allocation': '考核系数分配'
  }
  return nameMap[parameterName] || parameterName
}

// 获取数据来源显示名称
const getDataSourceDisplayName = (dataSource) => {
  const sourceMap = {
    'department_manager_score': '负责人评分表',
    'project_manager_score': '项目经理评分表',
    'assessment_coefficient_allocation': '考核系数分配表'
  }
  return sourceMap[dataSource] || dataSource
}

// 准备规则计算所需的数据
const prepareRuleCalculationData = async (userData, currentConfigId) => {
  try {
    // 从用户数据中提取参数评分
    const parameterScores = userData.parameterScores || []
    console.log('🔍 原始参数评分数据:', parameterScores)

    // 组织项目数据
    const projectData = []

    // 按项目分组参数评分
    const projectScoreMap = new Map()

    // 获取负责人评分 - 不设置默认值，如果没有评分数据就是null
    let departmentManagerScore = null
    let hasActualScore = false // 标记是否有实际的评分数据

    parameterScores.forEach(score => {
      console.log('🔍 处理参数评分:', score)

      // 处理负责人评分
      if (score.parameterName === 'department_manager_score') {
        if (score.scoreValue !== null && score.scoreValue !== undefined && score.scoreValue !== '') {
          departmentManagerScore = score.scoreValue
          hasActualScore = true
          console.log('🔍 设置负责人评分:', departmentManagerScore)
        }
      }

      // 处理项目相关评分
      if (score.projectId) {
        const projectKey = score.projectId
        if (!projectScoreMap.has(projectKey)) {
          // 如果没有项目名称，先设置为未知，后面会通过API获取
          let projectName = score.projectName || '未知项目'

          projectScoreMap.set(projectKey, {
            projectId: score.projectId,
            projectName: projectName,
            project_manager_score: 0,
            project_participation: 1.0,
            needsNameLookup: !score.projectName // 标记是否需要查找项目名称
          })
        }

        const project = projectScoreMap.get(projectKey)

        // 主要根据数据来源判断评分类型
        if (score.dataSource === 'project_manager_score') {
          // 项目经理评分
          project.project_manager_score = score.scoreValue || 0
          console.log('🔍 设置项目经理评分:', project.project_manager_score, '项目:', score.projectName)
        } else if (score.dataSource === 'assessment_coefficient_allocation') {
          // 项目参与度（考核系数分配）
          let participation = score.scoreValue || 100
          if (participation > 1) {
            participation = participation / 100
          }
          project.project_participation = participation
          console.log('🔍 设置项目参与度:', project.project_participation, '项目:', score.projectName)
        } else if (score.parameterName === 'project_manager_score') {
          // 备用：根据参数名判断
          project.project_manager_score = score.scoreValue || 0
          console.log('🔍 备用设置项目经理评分:', project.project_manager_score, '项目:', score.projectName)
        } else if (score.parameterName === 'project_participation') {
          // 备用：根据参数名判断
          let participation = score.scoreValue || 100
          if (participation > 1) {
            participation = participation / 100
          }
          project.project_participation = participation
          console.log('🔍 备用设置项目参与度:', project.project_participation, '项目:', score.projectName)
        }
      }
    })

    // 为需要查找名称的项目获取项目名称
    const projectsNeedingNames = []
    projectScoreMap.forEach((project, projectId) => {
      if (project.needsNameLookup) {
        projectsNeedingNames.push(projectId)
      }
    })

    // 并发获取项目名称
    if (projectsNeedingNames.length > 0) {
      console.log(`🔍 需要获取项目名称的项目ID:`, projectsNeedingNames)
      const namePromises = projectsNeedingNames.map(async (projectId) => {
        try {
          const projectRes = await findProjectInfo({ id: projectId })
          if (projectRes.code === 0 && projectRes.data && projectRes.data.name) {
            const project = projectScoreMap.get(projectId)
            if (project) {
              project.projectName = projectRes.data.name
              console.log(`✅ 更新项目名称: ID ${projectId} -> ${projectRes.data.name}`)
            }
          }
        } catch (error) {
          console.error(`❌ 获取项目名称失败，项目ID: ${projectId}`, error)
        }
      })

      await Promise.all(namePromises)
    }

    // 转换为规则计算需要的格式
    projectScoreMap.forEach((project, projectId) => {
      // 判断当前用户是否为该项目的负责人
      const isLeader = checkIfUserIsProjectLeader(userData.userName, projectId, currentConfigId)

      projectData.push({
        project_manager_score: project.project_manager_score,
        project_participation: project.project_participation,
        is_leader_of_this_project: isLeader,
        projectName: project.projectName, // 添加项目名称到项目数据中
        projectId: project.projectId
      })
    })

    // 如果没有项目数据，创建一个默认项目
    if (projectData.length === 0) {
      // 从参数评分中查找项目经理评分和参与度
      let projectManagerScore = 0
      let projectParticipation = 1.0

      parameterScores.forEach(score => {
        if (score.parameterName === 'project_manager_score') {
          projectManagerScore = score.scoreValue || 0
        } else if (score.parameterName === 'project_participation') {
          let participation = score.scoreValue || 100
          if (participation > 1) {
            participation = participation / 100
          }
          projectParticipation = participation
        }
      })

      projectData.push({
        project_manager_score: projectManagerScore,
        project_participation: projectParticipation,
        is_leader_of_this_project: false
      })
    }

    // 检查是否有实际的评分数据
    if (!hasActualScore || departmentManagerScore === null) {
      console.warn('🔍 用户没有实际的评分数据，无法进行规则计算')
      // 返回一个标记，表示没有评分数据
      return {
        department_manager_score: 0, // 使用0表示未评分
        project_data: projectData,
        hasNoScore: true // 标记没有评分数据
      }
    }

    const result = {
      department_manager_score: departmentManagerScore,
      project_data: projectData,
      hasNoScore: false
    }

    console.log('🔍 准备的规则计算数据:', result)
    return result
  } catch (error) {
    console.error('准备规则计算数据失败:', error)
    throw error
  }
}

// 检查用户是否为项目负责人
const checkIfUserIsProjectLeader = (username, projectId, configId) => {
  try {
    // 从当前的考核数据中查找项目负责人信息
    if (!assessmentData.value || !assessmentData.value.adminData) {
      return false
    }

    // 查找当前考核配置对应的项目数据
    const adminData = assessmentData.value.adminData

    // 遍历所有考核配置，找到匹配的项目
    for (const configKey in adminData) {
      const configData = adminData[configKey]
      if (configData && configData.projects) {
        for (const project of configData.projects) {
          if (project.id === projectId) {
            // 检查项目负责人
            return project.managerUsername === username ||
                   project.leaderUsername === username ||
                   project.manager === username
          }
        }
      }
    }

    return false
  } catch (error) {
    console.error('检查项目负责人失败:', error)
    return false
  }
}

// 查看员工详情
const viewEmployeeDetail = async (row) => {
  console.log('🚀 查看详情 - 开始获取用户详细信息')
  console.log('🚀 查看详情 - 用户行数据:', row)
  console.log('🚀 查看详情 - 用户名:', row.username)

  // 获取当前活跃tab的考核配置ID
  const currentTabName = editableTabsValue.value
  const currentTab = editableTabs.value.find(tab => tab.name === currentTabName)
  const currentConfigId = currentTab ? currentTab.configId : null

  console.log('🚀 查看详情 - 当前tab名称:', currentTabName)
  console.log('🚀 查看详情 - 当前tab信息:', currentTab)
  console.log('🚀 查看详情 - 当前考核配置ID:', currentConfigId)
  console.log('🚀 查看详情 - editableTabs.value:', editableTabs.value)

  // 显示加载状态
  const loadingMessage = ElMessage({
    type: 'info',
    message: '正在计算评分详情...',
    duration: 0
  })

  try {

    // 首先获取用户的基础数据和计算方法信息
    const userDataResponse = await getSingleUserParameterScores(
      row.username,
      currentConfigId ? [currentConfigId] : [],
      []
    )

    console.log('🔍 查看详情 - 用户基础数据:', userDataResponse)
    console.log('🔍 查看详情 - 当前考核配置ID:', currentConfigId)

    if (userDataResponse.code !== 0 || !userDataResponse.data?.users?.length) {
      throw new Error('获取用户数据失败')
    }

    const userData = userDataResponse.data.users[0]
    console.log('🔍 查看详情 - 用户数据:', userData)
    console.log('🔍 查看详情 - 计算方法:', userData.calculationMethod)

    // 检查是否有计算方法
    if (!userData.calculationMethod || !userData.calculationMethod.methodId) {
      console.error('🔍 查看详情 - 用户未分配计算方法')
      console.error('🔍 查看详情 - 用户名:', row.username)
      console.error('🔍 查看详情 - 考核配置ID:', currentConfigId)
      console.error('🔍 查看详情 - 返回的用户数据:', userData)
      const configName = currentTab ? currentTab.title : `配置ID: ${currentConfigId}`
      throw new Error(`用户 ${row.username} 未分配计算方法（考核配置: ${configName}）`)
    }

    // 组织规则计算所需的数据
    const ruleCalculationData = await prepareRuleCalculationData(userData, currentConfigId)
    console.log('🔍 查看详情 - 规则计算数据:', ruleCalculationData)

    // 检查是否有评分数据
    if (ruleCalculationData.hasNoScore) {
      // 关闭加载消息
      loadingMessage.close()

      // 显示没有评分数据的提示
      ElMessageBox.alert(
        `用户 ${userData.userNickName || userData.userName || row.username} 尚未填报评分数据，无法查看评分详情。`,
        '提示',
        {
          confirmButtonText: '确定',
          type: 'warning'
        }
      )
      return
    }

    // 调用规则计算API
    const ruleResponse = await calculateByRule({
      methodId: userData.calculationMethod.methodId,
      parameters: ruleCalculationData
    })

    console.log('🔍 查看详情 - 规则计算响应:', ruleResponse)

    // 关闭加载消息
    loadingMessage.close()

    if (ruleResponse.code === 0 && ruleResponse.data) {
      const calculationResult = ruleResponse.data
      console.log('🔍 查看详情 - 计算结果:', calculationResult)

      // 获取显示名称
      const displayName = userData.userNickName || userData.userName || row.username || '未知用户'
      const configName = currentTab ?
        (currentTab.label || currentTab.title || currentTab.name || currentTab.configName || `配置${currentTab.configId}`) :
        '未知配置'

      console.log('🔍 查看详情 - 显示名称:', displayName)
      console.log('🔍 查看详情 - 配置名称:', configName)

      // 构建详情HTML内容
      let detailHtml = `
        <div style="max-height: 600px; overflow-y: auto; font-family: Arial, sans-serif;">
          <div style="margin-bottom: 20px; border-bottom: 2px solid #ddd; padding-bottom: 10px;">
            <h3 style="margin: 0 0 5px 0; color: #333; font-size: 18px; font-weight: bold;">${displayName} 的评分详情</h3>
            <p style="margin: 0; color: #666; font-size: 14px;">考核配置: ${configName} (ID: ${currentConfigId || '未知'})</p>
          </div>
      `





      // 显示详细的参数数据
      detailHtml += await generateParameterDataDisplay(userData, ruleCalculationData, currentConfigId, calculationResult)

      detailHtml += `</div>`

      // 显示详情弹窗
      ElMessageBox({
        title: '评分详情',
        message: detailHtml,
        dangerouslyUseHTMLString: true,
        showCancelButton: false,
        confirmButtonText: '关闭',
        customClass: 'score-detail-dialog'
      })

    } else {
      ElMessage.error('规则计算失败: ' + (ruleResponse.msg || '未知错误'))
    }

  } catch (error) {
    console.error('🚫 查看详情 - 获取员工详细信息失败:', error)

    // 关闭加载提示
    loadingMessage.close()

    // 检查是否是"未分配计算方法"的错误
    if (error.message && error.message.includes('未分配计算方法')) {
      ElMessage.warning('该成员未配置计算方法，不参与本次考核')
    } else {
      ElMessage.error('获取员工详细信息失败: ' + error.message)
    }
  }
}

// 获取数据来源的中文名称





// 获取数据来源的中文名称
const getDataSourceName = (dataSource) => {
  const sourceMap = {
    'assessment_coefficient_allocation': '考核系数分配',
    'project_manager_score': '项目经理评分',
    'department_manager_score': '负责人评分'
  }
  return sourceMap[dataSource] || dataSource
}



// 计算最终得分
const calculateFinalScore = (coefficient, projectScore, departmentScore) => {
  // 如果没有评分数据，返回null
  if ((projectScore === null || projectScore === undefined) &&
      (departmentScore === null || departmentScore === undefined)) {
    return null
  }

  // 如果只有一个评分，使用该评分乘以系数
  if (projectScore !== null && projectScore !== undefined &&
      (departmentScore === null || departmentScore === undefined)) {
    return Number((coefficient * projectScore).toFixed(2))
  }

  if (departmentScore !== null && departmentScore !== undefined &&
      (projectScore === null || projectScore === undefined)) {
    return Number((coefficient * departmentScore).toFixed(2))
  }

  // 如果两个评分都有，使用加权平均（这里可以根据实际业务逻辑调整权重）
  if (projectScore !== null && projectScore !== undefined &&
      departmentScore !== null && departmentScore !== undefined) {
    // 项目负责人评分权重40%，负责人评分权重60%
    const weightedScore = projectScore * 0.4 + departmentScore * 0.6
    return Number((coefficient * weightedScore).toFixed(2))
  }

  return null
}

// 计算表格高度（自适应窗口大小）
const calculateTableHeight = () => {
  // 计算可用高度：窗口高度 - 头部导航 - 标签页 - 操作按钮区域 - 底部边距
  const windowHeight = window.innerHeight
  const headerHeight = 60  // 头部导航高度
  const tabsHeight = 120   // 标签页高度（包括主标签和子标签）
  const buttonAreaHeight = 80  // 操作按钮区域高度
  const bottomMargin = 40  // 底部边距

  const availableHeight = windowHeight - headerHeight - tabsHeight - buttonAreaHeight - bottomMargin

  // 最小高度400px，最大高度不超过可用高度
  const height = Math.max(400, Math.min(availableHeight, 800))
  tableHeight.value = height
  return height
}

// 获取表格高度
const getTableHeight = () => {
  return tableHeight.value
}




</script>

<style scoped>
/* 项目考核评分容器 */
.project-assessment-score {
  width: 100%;
  height: 100%;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #666;
}

.loading-container p {
  margin-top: 16px;
  font-size: 14px;
}

/* 无数据状态样式 */
.no-data-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  padding: 40px;
}

.no-data-container .el-empty {
  padding: 40px 0;
}

.no-data-container .el-empty__description p {
  margin: 8px 0;
  color: #909399;
  font-size: 14px;
}

/* Tabs 样式 */
.demo-tabs {
  margin: 10px; /* 减少外边距，节省空间 */
}

.demo-tabs .el-tabs__content {
  padding: 10px; /* 减少padding，节省空间 */
  background: #fff;
  border-radius: 0 0 8px 8px;
  min-height: auto; /* 移除固定最小高度，让内容自适应 */
  position: relative; /* 确保 sticky 定位的父容器正确 */
  overflow: visible; /* 确保 sticky 元素可见 */
}

/* 自定义子标签页样式 */
.custom-sub-tabs {
  width: 100%;
}

.custom-tabs-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
  background: #fff;
}

.custom-tab-item {
  padding: 8px 20px;
  margin: 0 4px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  user-select: none;
  position: relative;
}

.custom-tab-item:hover {
  color: #409eff;
}

.custom-tab-item.active {
  color: #409eff;
  border-bottom-color: #409eff;
}

.custom-tabs-content {
  width: 100%;
}

.custom-tab-content {
  width: 100%;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 子标签页样式 */
.sub-tabs {
  margin-top: 0;
}

.sub-tabs .el-tabs__header {
  margin: 0 0 20px 0;
}

.sub-tabs .el-tabs__content {
  padding: 0;
  background: transparent;
}

/* 空标签页内容样式 */
.empty-tab-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
}

/* 员工评分表格样式 */
.employee-score-table-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-top: 16px;
}

.employee-score-table-container .el-table {
  border-radius: 8px;
}

.employee-score-table-container .el-table__header-wrapper {
  border-radius: 8px 8px 0 0;
}

.employee-score-table-container .el-table__body-wrapper {
  max-height: 600px;
  overflow-y: auto;
}

/* 员工评分表格行样式 */
.employee-score-table-container .el-table__row:hover {
  background-color: #f5f7fa;
}

/* 员工评分表格单元格样式 */
.employee-score-table-container .el-table td,
.employee-score-table-container .el-table th {
  padding: 12px 8px;
  text-align: center;
}

/* 固定列样式 */
.employee-score-table-container .el-table__fixed-column--left {
  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
}

/* 数值列样式 */
.employee-score-table-container .el-table__cell {
  font-variant-numeric: tabular-nums;
}

/* 未评分状态样式 */
.text-gray-400 {
  color: #9ca3af;
  font-style: italic;
}

/* 最终得分样式 */
.final-score {
  color: #409eff;
  font-weight: bold;
  font-size: 14px;
}

/* 操作按钮样式 */
.operation-buttons {
  display: flex;
  gap: 6px;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  width: 100%;
}

.operation-buttons .el-button {
  margin: 0;
  font-size: 12px;
  padding: 4px 8px;
  flex-shrink: 0;
}

.operation-buttons .el-button--small {
  height: 28px;
  line-height: 1;
}

/* 操作列固定样式 */
.employee-score-table-container .el-table__fixed-column--right {
  box-shadow: -2px 0 6px rgba(0, 0, 0, 0.1);
}

/* 操作按钮样式 */
.operation-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.operation-buttons .el-button {
  margin: 2px;
  font-size: 12px;
  padding: 4px 8px;
}

.operation-buttons .el-button--small {
  height: 28px;
  line-height: 1;
}

/* 操作列固定样式 */
.employee-score-table-container .el-table__fixed-column--right {
  box-shadow: -2px 0 6px rgba(0, 0, 0, 0.1);
}

/* 确保 tabs 内的表格容器支持 sticky */
.demo-tabs .assessment-table-container {
  position: relative;
  overflow: visible;
}

.demo-tabs .table-wrapper {
  position: relative;
  overflow-x: auto; /* 当内容超出时显示水平滚动条 */
  overflow-y: auto; /* 保留垂直滚动条 */
}

.assessment-table-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
  margin-bottom: 10px; /* 减少底部边距 */
}

.table-wrapper {
  overflow-x: auto; /* 当内容超出时显示水平滚动条 */
  overflow-y: auto; /* 保留垂直滚动条 */
  max-height: 85vh; /* 最大化表格显示区域 */
  min-height: auto; /* 移除固定最小高度，让内容自适应 */
  width: 100%;
  position: relative;
  border: 1px solid #e4e7ed;
  /* 确保sticky bottom定位正确工作 */
  /* 确保合计行始终可见 */
  contain: layout style;
  /* 为sticky定位创建新的堆叠上下文 */
  transform: translateZ(0);
}

/* 自定义滚动条样式 */
.table-wrapper::-webkit-scrollbar {
  width: 8px; /* 垂直滚动条宽度 */
  height: 8px; /* 水平滚动条高度 */
}

.table-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.table-wrapper::-webkit-scrollbar-corner {
  background: #f1f1f1;
}

/* 滚动条样式 */
.table-wrapper {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* 确保sticky定位的父容器正确 */
.assessment-table thead,
.assessment-table tbody {
  position: relative;
}

.assessment-table {
  width: 100%;
  min-width: 800px; /* 设置最小宽度，确保在窗口缩小时触发滚动条 */
  border-collapse: separate;
  border-spacing: 0;
  font-size: 14px;
  background: #fff;
  table-layout: auto; /* 自动表格布局，让成员列自适应 */
  overflow-wrap: break-word; /* 确保长文本换行 */
}

.table-header {
  position: -webkit-sticky !important;
  position: sticky !important;
  top: 0px !important;
  z-index: 250 !important;
  background: #f5f7fa;
}

.assessment-table th,
.assessment-table td {
  border-right: 1px solid #e4e7ed;
  border-bottom: 1px solid #e4e7ed;
  text-align: center;
  vertical-align: middle;
  position: relative;
}

.assessment-table th:first-child,
.assessment-table td:first-child {
  border-left: 1px solid #e4e7ed;
}

.assessment-table thead th {
  border-top: 1px solid #e4e7ed;
  position: sticky;
  top: 0;
  background: #f5f7fa;
  z-index: 4;
}

/* 表头样式 */
.project-header {
  background: #f5f7fa !important;
  color: #303133;
  font-weight: 600;
  padding: 8px 6px; /* 减少padding，使表格更紧凑 */
  min-width: 200px !important; /* 与项目名称列保持一致 */
  width: 200px !important; /* 强制固定宽度 */
  max-width: 200px !important; /* 限制最大宽度 */
  position: -webkit-sticky !important;
  position: sticky !important;
  left: 0px !important;
  top: 0px !important;
  z-index: 300 !important;
  border-right: 2px solid #dcdfe6;
  transition: all 0.2s ease;
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  word-wrap: break-word; /* 允许单词内换行 */
  word-break: break-all; /* 强制换行 */
  white-space: normal; /* 允许换行 */
  overflow-wrap: break-word; /* 现代浏览器的换行属性 */
  text-align: center; /* 表头文字居中 */
  line-height: 1.3; /* 减少行高，使表格更紧凑 */
}

.member-header {
  background: #f5f7fa !important;
  color: #303133;
  font-weight: 600;
  padding: 6px 3px; /* 减少padding，使表格更紧凑 */
  min-width: 90px; /* 增加最小宽度确保可读性 */
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 10;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  text-align: center;
  white-space: nowrap; /* 防止成员名称换行 */
}

.member-header.col-highlight {
  background: #e6f7ff !important;
  color: #409eff;
}



/* 项目名称列样式 */
.project-name {
  background: #fafafa !important;
  padding: 8px 6px; /* 减少padding，使表格更紧凑 */
  min-width: 200px; /* 固定最小宽度 */
  width: 200px !important; /* 强制固定宽度 */
  max-width: 200px !important; /* 限制最大宽度 */
  position: -webkit-sticky !important;
  position: sticky !important;
  left: 0px !important;
  z-index: 50 !important;
  border-right: 2px solid #dcdfe6;
  transition: all 0.2s ease;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  word-wrap: break-word; /* 允许单词内换行 */
  word-break: break-all; /* 强制换行 */
  white-space: normal; /* 允许换行 */
  overflow-wrap: break-word; /* 现代浏览器的换行属性 */
  hyphens: auto; /* 自动连字符 */
}

.project-name.row-highlight {
  background: #e6f7ff !important;
}

.project-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
  line-height: 1.4; /* 增加行高便于阅读 */
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}

/* 成员行样式 */
.member-row {
  border-bottom: 1px solid #e4e7ed;
}

.member-row:hover {
  background-color: #f5f7fa;
}

.member-name {
  background: #fafafa !important;
  font-weight: 600;
  color: #303133;
  text-align: center;
  padding: 8px 6px;
  border-right: 2px solid #dcdfe6;
  min-width: 120px !important;
  width: 120px !important;
  max-width: 120px !important;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  line-height: 1.4;
  display: table-cell !important;
  transform: translateZ(0);
  will-change: transform;
}

.member-title {
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  padding: 2px 0;
}

/* 评分单元格样式 */
.score-cell {
  padding: 6px 3px; /* 减少padding，使表格更紧凑 */
  background: #fff;
  transition: all 0.2s ease;
  cursor: text;
  font-weight: 500;
  color: #303133;
  min-width: 90px; /* 增加最小宽度确保可读性 */
  position: relative;
  text-align: center;
}

/* 十字高亮效果 */
.score-cell.row-highlight {
  background: #f0f9ff !important;
}

.score-cell.col-highlight {
  background: #f0f9ff !important;
}

.score-cell.current-highlight {
  background: #e6f7ff !important;
  box-shadow: inset 0 0 0 2px #409eff;
  transform: scale(1.02);
  z-index: 10;
}

/* 聚焦状态 */
.score-cell:focus {
  outline: none;
  background: #fff !important;
  box-shadow: inset 0 0 0 2px #409eff;
  border-radius: 4px;
  transform: scale(1.02);
  z-index: 15;
}

/* 可编辑状态提示 */
.score-cell::before {
  content: '';
  position: absolute;
  top: 2px;
  right: 2px;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-top: 6px solid #e6f7ff;
  opacity: 0;
  transition: opacity 0.2s;
}

.score-cell.current-highlight::before {
  opacity: 1;
  border-top-color: #409eff;
}

/* 项目行样式 */
.project-row:nth-child(even) .score-cell {
  background: #fafbfc;
}

/* 移除原来的行悬停效果，使用十字高亮代替 */

/* 合计行样式 */
.assessment-table tfoot {
  background: #f5f7fa;
  font-weight: 600;
  position: -webkit-sticky !important;
  position: sticky !important;
  bottom: 0px !important;
  z-index: 999 !important;
  display: table-footer-group !important;
  /* 确保在滚动容器中正确显示 */
  transform: translateZ(0);
  will-change: transform;
}

.total-row {
  background: #f5f7fa !important;
  position: -webkit-sticky !important;
  position: sticky !important;
  bottom: 0px !important;
  z-index: 999 !important;
  display: table-row !important;
  /* 确保在滚动容器中正确显示 */
  transform: translateZ(0);
  will-change: transform;
}

.total-label {
  background: #f5f7fa !important;
  color: #409eff !important;
  font-weight: 600;
  text-align: center;
  padding: 8px 6px; /* 减少padding，使表格更紧凑 */
  position: -webkit-sticky !important;
  position: sticky !important;
  left: 0px !important;
  bottom: 0px !important;
  z-index: 1000 !important; /* 最高层级，确保在交叉位置正确显示 */
  border-right: 2px solid #dcdfe6;
  min-width: 200px !important;
  width: 200px !important;
  max-width: 200px !important;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  line-height: 1.4;
  display: table-cell !important;
  /* 确保在滚动容器中正确显示 */
  transform: translateZ(0);
  will-change: transform;
}

.total-value {
  background: #f5f7fa !important;
  color: #409eff !important;
  font-weight: 600;
  text-align: center;
  padding: 6px 3px; /* 减少padding，使表格更紧凑 */
  min-width: 90px; /* 与成员列保持一致 */
  border-right: 1px solid #e4e7ed;
  border-bottom: 1px solid #e4e7ed;
  position: -webkit-sticky !important;
  position: sticky !important;
  bottom: 0px !important;
  z-index: 999 !important;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
  display: table-cell !important;
  /* 确保在滚动容器中正确显示 */
  transform: translateZ(0);
  will-change: transform;
}

/* 强制合计行固定在底部 - 兼容性增强 */
.assessment-table-container .table-wrapper {
  /* 确保sticky定位的父容器正确 */
  position: relative;
  overflow: auto;
}

.assessment-table-container .assessment-table tfoot,
.assessment-table-container .total-row,
.assessment-table-container .total-label,
.assessment-table-container .total-value {
  /* 强制sticky定位 */
  position: -webkit-sticky !important;
  position: sticky !important;
  bottom: 0px !important;
}

.assessment-table-container .total-label {
  left: 0px !important;
  z-index: 1000 !important;
}

.assessment-table-container .total-value {
  z-index: 999 !important;
}

.assessment-table-container .assessment-table tfoot {
  z-index: 999 !important;
}

.assessment-table-container .total-row {
  z-index: 999 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .assessment-table {
    font-size: 12px;
  }

  .project-header {
    min-width: 150px !important;
    width: 150px !important;
    max-width: 150px !important;
    padding: 8px 4px;
    font-size: 12px;
  }

  .member-header {
    min-width: 70px; /* 移动端也保持合理的最小宽度 */
    padding: 6px 2px;
    font-size: 11px;
  }

  .score-cell {
    min-width: 70px; /* 移动端也保持合理的最小宽度 */
    padding: 6px 2px;
    font-size: 12px;
  }

  .project-name {
    min-width: 150px !important;
    width: 150px !important;
    max-width: 150px !important;
    padding: 8px 4px;
    font-size: 12px;
  }

  .project-title {
    font-size: 12px;
    line-height: 1.3;
  }





  .total-label {
    min-width: 150px !important;
    width: 150px !important;
    max-width: 150px !important;
    padding: 8px 4px;
    font-size: 12px;
  }

  .total-value {
    min-width: 70px; /* 移动端也保持合理的最小宽度 */
    padding: 6px 2px;
    font-size: 11px;
  }
}

/* 不可编辑单元格样式 */
.disabled-cell {
  background-color: #f5f7fa !important;
  color: #c0c4cc !important;
  cursor: not-allowed !important;
  opacity: 0.8;
  text-align: center;
  font-weight: bold;
  font-size: 16px;
}

.disabled-cell:hover {
  background-color: #f5f7fa !important;
}

.disabled-cell:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* 可编辑单元格的强调样式 */
.score-cell:not(.disabled-cell) {
  background-color: #ffffff;
  cursor: text;
}

.score-cell:not(.disabled-cell):hover {
  background-color: #f0f9ff;
  border-color: #409eff;
}

.score-cell:not(.disabled-cell):focus {
  background-color: #f0f9ff;
  border-color: #409eff;
  outline: 2px solid #409eff;
  outline-offset: -2px;
}

/* 重要确认对话框样式 */
:deep(.important-confirm-dialog) {
  .el-message-box__message {
    font-size: 16px;
    line-height: 1.6;
    color: #e6a23c;
    font-weight: 500;
  }

  .el-message-box__title {
    font-size: 18px;
    font-weight: 600;
    color: #e6a23c;
  }

  .el-message-box__content {
    padding: 20px 24px;
  }

  .el-button--primary {
    background-color: #e6a23c;
    border-color: #e6a23c;
  }

  .el-button--primary:hover {
    background-color: #ebb563;
    border-color: #ebb563;
  }
}

/* 考核系数分配表格容器样式 */
.coefficient-table-container {
  margin-top: 16px;
}

/* 项目成员评分表格容器样式 */
.project-member-table-container {
  margin-top: 16px;
}

/* 高分限制信息样式 */
.high-score-limit-info {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background-color: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 6px;
  font-size: 14px;
  max-width: 400px;
}

.limit-text {
  color: #92400e;
  font-weight: 500;
  white-space: nowrap;
}

.limit-details {
  color: #d97706;
  font-weight: bold;
  font-size: 13px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 员工评分操作按钮样式 */
.employee-action-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
  align-items: center;
}

.employee-action-buttons .el-button {
  margin: 0;
  padding: 8px 16px;
  font-size: 14px;
}

/* 配额信息样式 */
.quota-info {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  font-size: 14px;
}

.quota-text {
  color: #1e40af;
  font-weight: 500;
}

.quota-number {
  color: #dc2626;
  font-weight: bold;
  font-size: 16px;
}

/* 奖金信息样式 */
.bonus-info {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background-color: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 6px;
  font-size: 14px;
}

.bonus-text {
  color: #166534;
  font-weight: 500;
}

.bonus-number {
  color: #059669;
  font-weight: bold;
  font-size: 16px;
}

/* 禁用评分状态样式 */
.disabled-score {
  color: #c0c4cc;
  font-style: italic;
  font-size: 16px;
  font-weight: bold;
}

/* 禁用按钮样式 */
.operation-buttons .el-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 员工评分表格可编辑单元格样式 - 与考核系数分配表格一致 */
.score-cell {
  min-height: 32px;
  line-height: 32px;
  padding: 4px 8px;
  text-align: center;
  font-weight: 500;
  transition: all 0.2s ease;
}

.score-cell.editable-cell {
  cursor: text;
  background-color: transparent;
  border: none;
  color: #606266;
}

.score-cell.editable-cell:hover {
  background-color: rgba(64, 158, 255, 0.05);
  border-radius: 4px;
}

.score-cell.editable-cell:focus {
  outline: none;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 4px;
  box-shadow: inset 0 0 0 1px #409eff;
}

.score-cell.disabled-cell {
  background-color: transparent;
  color: #c0c4cc;
  cursor: not-allowed;
  border: none;
}

/* 考核系数分配不可编辑单元格样式 */
.coefficient-cell.non-editable-cell {
  background-color: #f5f7fa;
  color: #909399;
  cursor: not-allowed;
  border: none;
  min-height: 32px;
  line-height: 32px;
  padding: 4px 8px;
  text-align: center;
  font-weight: 500;
  border-radius: 4px;
  user-select: none;
}

.coefficient-cell.non-editable-cell:hover {
  background-color: #f5f7fa;
  color: #909399;
}

.score-cell.cell-highlight {
  background-color: rgba(255, 193, 7, 0.1);
  border-radius: 4px;
}



/* 禁用行样式 */
.el-table__row.disabled-row {
  background-color: rgba(192, 196, 204, 0.05);
  color: #c0c4cc;
}

.el-table__row.disabled-row:hover {
  background-color: rgba(192, 196, 204, 0.1);
}

/* 复制粘贴相关样式 */
.editable-cell {
  position: relative;
  transition: all 0.3s ease;
}

.editable-cell:focus {
  outline: 2px solid #409eff;
  outline-offset: -2px;
  background-color: #f0f9ff;
}

/* 复制反馈动画 */
@keyframes copyFeedback {
  0% {
    background-color: #e6f7ff;
    border-color: #1890ff;
  }
  100% {
    background-color: transparent;
    border-color: transparent;
  }
}

.copy-feedback {
  animation: copyFeedback 0.3s ease-out;
}

/* 粘贴成功反馈 */
@keyframes pasteSuccess {
  0% {
    background-color: #f6ffed;
    border-color: #52c41a;
  }
  100% {
    background-color: transparent;
    border-color: transparent;
  }
}

.paste-success {
  animation: pasteSuccess 0.5s ease-out;
}

</style>
